
/**
 * Client
**/

import * as runtime from './runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model users
 * 
 */
export type users = $Result.DefaultSelection<Prisma.$usersPayload>
/**
 * Model user_settings
 * 
 */
export type user_settings = $Result.DefaultSelection<Prisma.$user_settingsPayload>
/**
 * Model medications
 * 
 */
export type medications = $Result.DefaultSelection<Prisma.$medicationsPayload>
/**
 * Model adherence
 * 
 */
export type adherence = $Result.DefaultSelection<Prisma.$adherencePayload>
/**
 * Model reminders
 * 
 */
export type reminders = $Result.DefaultSelection<Prisma.$remindersPayload>

/**
 * ##  Prisma Client ʲˢ
 *
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Users
 * const users = await prisma.users.findMany()
 * ```
 *
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   *
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more Users
   * const users = await prisma.users.findMany()
   * ```
   *
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<ClientOptions>, ExtArgs, $Utils.Call<Prisma.TypeMapCb<ClientOptions>, {
    extArgs: ExtArgs
  }>>

      /**
   * `prisma.users`: Exposes CRUD operations for the **users** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Users
    * const users = await prisma.users.findMany()
    * ```
    */
  get users(): Prisma.usersDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.user_settings`: Exposes CRUD operations for the **user_settings** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more User_settings
    * const user_settings = await prisma.user_settings.findMany()
    * ```
    */
  get user_settings(): Prisma.user_settingsDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.medications`: Exposes CRUD operations for the **medications** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Medications
    * const medications = await prisma.medications.findMany()
    * ```
    */
  get medications(): Prisma.medicationsDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.adherence`: Exposes CRUD operations for the **adherence** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Adherences
    * const adherences = await prisma.adherence.findMany()
    * ```
    */
  get adherence(): Prisma.adherenceDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.reminders`: Exposes CRUD operations for the **reminders** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Reminders
    * const reminders = await prisma.reminders.findMany()
    * ```
    */
  get reminders(): Prisma.remindersDelegate<ExtArgs, ClientOptions>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 6.9.0
   * Query Engine version: 81e4af48011447c3cc503a190e86995b66d2a28e
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    users: 'users',
    user_settings: 'user_settings',
    medications: 'medications',
    adherence: 'adherence',
    reminders: 'reminders'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb<ClientOptions = {}> extends $Utils.Fn<{extArgs: $Extensions.InternalArgs }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
    globalOmitOptions: {
      omit: GlobalOmitOptions
    }
    meta: {
      modelProps: "users" | "user_settings" | "medications" | "adherence" | "reminders"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      users: {
        payload: Prisma.$usersPayload<ExtArgs>
        fields: Prisma.usersFieldRefs
        operations: {
          findUnique: {
            args: Prisma.usersFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$usersPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.usersFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$usersPayload>
          }
          findFirst: {
            args: Prisma.usersFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$usersPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.usersFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$usersPayload>
          }
          findMany: {
            args: Prisma.usersFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$usersPayload>[]
          }
          create: {
            args: Prisma.usersCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$usersPayload>
          }
          createMany: {
            args: Prisma.usersCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.usersCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$usersPayload>[]
          }
          delete: {
            args: Prisma.usersDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$usersPayload>
          }
          update: {
            args: Prisma.usersUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$usersPayload>
          }
          deleteMany: {
            args: Prisma.usersDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.usersUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.usersUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$usersPayload>[]
          }
          upsert: {
            args: Prisma.usersUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$usersPayload>
          }
          aggregate: {
            args: Prisma.UsersAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateUsers>
          }
          groupBy: {
            args: Prisma.usersGroupByArgs<ExtArgs>
            result: $Utils.Optional<UsersGroupByOutputType>[]
          }
          count: {
            args: Prisma.usersCountArgs<ExtArgs>
            result: $Utils.Optional<UsersCountAggregateOutputType> | number
          }
        }
      }
      user_settings: {
        payload: Prisma.$user_settingsPayload<ExtArgs>
        fields: Prisma.user_settingsFieldRefs
        operations: {
          findUnique: {
            args: Prisma.user_settingsFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$user_settingsPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.user_settingsFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$user_settingsPayload>
          }
          findFirst: {
            args: Prisma.user_settingsFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$user_settingsPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.user_settingsFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$user_settingsPayload>
          }
          findMany: {
            args: Prisma.user_settingsFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$user_settingsPayload>[]
          }
          create: {
            args: Prisma.user_settingsCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$user_settingsPayload>
          }
          createMany: {
            args: Prisma.user_settingsCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.user_settingsCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$user_settingsPayload>[]
          }
          delete: {
            args: Prisma.user_settingsDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$user_settingsPayload>
          }
          update: {
            args: Prisma.user_settingsUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$user_settingsPayload>
          }
          deleteMany: {
            args: Prisma.user_settingsDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.user_settingsUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.user_settingsUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$user_settingsPayload>[]
          }
          upsert: {
            args: Prisma.user_settingsUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$user_settingsPayload>
          }
          aggregate: {
            args: Prisma.User_settingsAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateUser_settings>
          }
          groupBy: {
            args: Prisma.user_settingsGroupByArgs<ExtArgs>
            result: $Utils.Optional<User_settingsGroupByOutputType>[]
          }
          count: {
            args: Prisma.user_settingsCountArgs<ExtArgs>
            result: $Utils.Optional<User_settingsCountAggregateOutputType> | number
          }
        }
      }
      medications: {
        payload: Prisma.$medicationsPayload<ExtArgs>
        fields: Prisma.medicationsFieldRefs
        operations: {
          findUnique: {
            args: Prisma.medicationsFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$medicationsPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.medicationsFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$medicationsPayload>
          }
          findFirst: {
            args: Prisma.medicationsFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$medicationsPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.medicationsFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$medicationsPayload>
          }
          findMany: {
            args: Prisma.medicationsFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$medicationsPayload>[]
          }
          create: {
            args: Prisma.medicationsCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$medicationsPayload>
          }
          createMany: {
            args: Prisma.medicationsCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.medicationsCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$medicationsPayload>[]
          }
          delete: {
            args: Prisma.medicationsDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$medicationsPayload>
          }
          update: {
            args: Prisma.medicationsUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$medicationsPayload>
          }
          deleteMany: {
            args: Prisma.medicationsDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.medicationsUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.medicationsUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$medicationsPayload>[]
          }
          upsert: {
            args: Prisma.medicationsUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$medicationsPayload>
          }
          aggregate: {
            args: Prisma.MedicationsAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateMedications>
          }
          groupBy: {
            args: Prisma.medicationsGroupByArgs<ExtArgs>
            result: $Utils.Optional<MedicationsGroupByOutputType>[]
          }
          count: {
            args: Prisma.medicationsCountArgs<ExtArgs>
            result: $Utils.Optional<MedicationsCountAggregateOutputType> | number
          }
        }
      }
      adherence: {
        payload: Prisma.$adherencePayload<ExtArgs>
        fields: Prisma.adherenceFieldRefs
        operations: {
          findUnique: {
            args: Prisma.adherenceFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$adherencePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.adherenceFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$adherencePayload>
          }
          findFirst: {
            args: Prisma.adherenceFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$adherencePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.adherenceFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$adherencePayload>
          }
          findMany: {
            args: Prisma.adherenceFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$adherencePayload>[]
          }
          create: {
            args: Prisma.adherenceCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$adherencePayload>
          }
          createMany: {
            args: Prisma.adherenceCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.adherenceCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$adherencePayload>[]
          }
          delete: {
            args: Prisma.adherenceDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$adherencePayload>
          }
          update: {
            args: Prisma.adherenceUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$adherencePayload>
          }
          deleteMany: {
            args: Prisma.adherenceDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.adherenceUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.adherenceUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$adherencePayload>[]
          }
          upsert: {
            args: Prisma.adherenceUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$adherencePayload>
          }
          aggregate: {
            args: Prisma.AdherenceAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateAdherence>
          }
          groupBy: {
            args: Prisma.adherenceGroupByArgs<ExtArgs>
            result: $Utils.Optional<AdherenceGroupByOutputType>[]
          }
          count: {
            args: Prisma.adherenceCountArgs<ExtArgs>
            result: $Utils.Optional<AdherenceCountAggregateOutputType> | number
          }
        }
      }
      reminders: {
        payload: Prisma.$remindersPayload<ExtArgs>
        fields: Prisma.remindersFieldRefs
        operations: {
          findUnique: {
            args: Prisma.remindersFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$remindersPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.remindersFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$remindersPayload>
          }
          findFirst: {
            args: Prisma.remindersFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$remindersPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.remindersFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$remindersPayload>
          }
          findMany: {
            args: Prisma.remindersFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$remindersPayload>[]
          }
          create: {
            args: Prisma.remindersCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$remindersPayload>
          }
          createMany: {
            args: Prisma.remindersCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.remindersCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$remindersPayload>[]
          }
          delete: {
            args: Prisma.remindersDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$remindersPayload>
          }
          update: {
            args: Prisma.remindersUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$remindersPayload>
          }
          deleteMany: {
            args: Prisma.remindersDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.remindersUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.remindersUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$remindersPayload>[]
          }
          upsert: {
            args: Prisma.remindersUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$remindersPayload>
          }
          aggregate: {
            args: Prisma.RemindersAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateReminders>
          }
          groupBy: {
            args: Prisma.remindersGroupByArgs<ExtArgs>
            result: $Utils.Optional<RemindersGroupByOutputType>[]
          }
          count: {
            args: Prisma.remindersCountArgs<ExtArgs>
            result: $Utils.Optional<RemindersCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Defaults to stdout
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events
     * log: [
     *   { emit: 'stdout', level: 'query' },
     *   { emit: 'stdout', level: 'info' },
     *   { emit: 'stdout', level: 'warn' }
     *   { emit: 'stdout', level: 'error' }
     * ]
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
    /**
     * Global configuration for omitting model fields by default.
     * 
     * @example
     * ```
     * const prisma = new PrismaClient({
     *   omit: {
     *     user: {
     *       password: true
     *     }
     *   }
     * })
     * ```
     */
    omit?: Prisma.GlobalOmitConfig
  }
  export type GlobalOmitConfig = {
    users?: usersOmit
    user_settings?: user_settingsOmit
    medications?: medicationsOmit
    adherence?: adherenceOmit
    reminders?: remindersOmit
  }

  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type GetLogType<T extends LogLevel | LogDefinition> = T extends LogDefinition ? T['emit'] extends 'event' ? T['level'] : never : never
  export type GetEvents<T extends any> = T extends Array<LogLevel | LogDefinition> ?
    GetLogType<T[0]> | GetLogType<T[1]> | GetLogType<T[2]> | GetLogType<T[3]>
    : never

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'updateManyAndReturn'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */


  /**
   * Count Type UsersCountOutputType
   */

  export type UsersCountOutputType = {
    adherence: number
    medication: number
    reminders: number
  }

  export type UsersCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    adherence?: boolean | UsersCountOutputTypeCountAdherenceArgs
    medication?: boolean | UsersCountOutputTypeCountMedicationArgs
    reminders?: boolean | UsersCountOutputTypeCountRemindersArgs
  }

  // Custom InputTypes
  /**
   * UsersCountOutputType without action
   */
  export type UsersCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UsersCountOutputType
     */
    select?: UsersCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * UsersCountOutputType without action
   */
  export type UsersCountOutputTypeCountAdherenceArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: adherenceWhereInput
  }

  /**
   * UsersCountOutputType without action
   */
  export type UsersCountOutputTypeCountMedicationArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: medicationsWhereInput
  }

  /**
   * UsersCountOutputType without action
   */
  export type UsersCountOutputTypeCountRemindersArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: remindersWhereInput
  }


  /**
   * Count Type MedicationsCountOutputType
   */

  export type MedicationsCountOutputType = {
    adherence: number
    reminders: number
  }

  export type MedicationsCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    adherence?: boolean | MedicationsCountOutputTypeCountAdherenceArgs
    reminders?: boolean | MedicationsCountOutputTypeCountRemindersArgs
  }

  // Custom InputTypes
  /**
   * MedicationsCountOutputType without action
   */
  export type MedicationsCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MedicationsCountOutputType
     */
    select?: MedicationsCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * MedicationsCountOutputType without action
   */
  export type MedicationsCountOutputTypeCountAdherenceArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: adherenceWhereInput
  }

  /**
   * MedicationsCountOutputType without action
   */
  export type MedicationsCountOutputTypeCountRemindersArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: remindersWhereInput
  }


  /**
   * Count Type AdherenceCountOutputType
   */

  export type AdherenceCountOutputType = {
    reminders: number
  }

  export type AdherenceCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    reminders?: boolean | AdherenceCountOutputTypeCountRemindersArgs
  }

  // Custom InputTypes
  /**
   * AdherenceCountOutputType without action
   */
  export type AdherenceCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AdherenceCountOutputType
     */
    select?: AdherenceCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * AdherenceCountOutputType without action
   */
  export type AdherenceCountOutputTypeCountRemindersArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: remindersWhereInput
  }


  /**
   * Models
   */

  /**
   * Model users
   */

  export type AggregateUsers = {
    _count: UsersCountAggregateOutputType | null
    _min: UsersMinAggregateOutputType | null
    _max: UsersMaxAggregateOutputType | null
  }

  export type UsersMinAggregateOutputType = {
    id: string | null
    email: string | null
    name: string | null
    password: string | null
    date_of_birth: Date | null
    gender: string | null
    is_admin: boolean | null
    phone_number: string | null
    created_at: Date | null
    updated_at: Date | null
    subscription_status: string | null
    subscription_plan: string | null
    subscription_expires_at: Date | null
  }

  export type UsersMaxAggregateOutputType = {
    id: string | null
    email: string | null
    name: string | null
    password: string | null
    date_of_birth: Date | null
    gender: string | null
    is_admin: boolean | null
    phone_number: string | null
    created_at: Date | null
    updated_at: Date | null
    subscription_status: string | null
    subscription_plan: string | null
    subscription_expires_at: Date | null
  }

  export type UsersCountAggregateOutputType = {
    id: number
    email: number
    name: number
    password: number
    date_of_birth: number
    gender: number
    allergies: number
    conditions: number
    is_admin: number
    phone_number: number
    emergency_contact: number
    created_at: number
    updated_at: number
    subscription_status: number
    subscription_plan: number
    subscription_expires_at: number
    subscription_features: number
    _all: number
  }


  export type UsersMinAggregateInputType = {
    id?: true
    email?: true
    name?: true
    password?: true
    date_of_birth?: true
    gender?: true
    is_admin?: true
    phone_number?: true
    created_at?: true
    updated_at?: true
    subscription_status?: true
    subscription_plan?: true
    subscription_expires_at?: true
  }

  export type UsersMaxAggregateInputType = {
    id?: true
    email?: true
    name?: true
    password?: true
    date_of_birth?: true
    gender?: true
    is_admin?: true
    phone_number?: true
    created_at?: true
    updated_at?: true
    subscription_status?: true
    subscription_plan?: true
    subscription_expires_at?: true
  }

  export type UsersCountAggregateInputType = {
    id?: true
    email?: true
    name?: true
    password?: true
    date_of_birth?: true
    gender?: true
    allergies?: true
    conditions?: true
    is_admin?: true
    phone_number?: true
    emergency_contact?: true
    created_at?: true
    updated_at?: true
    subscription_status?: true
    subscription_plan?: true
    subscription_expires_at?: true
    subscription_features?: true
    _all?: true
  }

  export type UsersAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which users to aggregate.
     */
    where?: usersWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of users to fetch.
     */
    orderBy?: usersOrderByWithRelationInput | usersOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: usersWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned users
    **/
    _count?: true | UsersCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: UsersMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: UsersMaxAggregateInputType
  }

  export type GetUsersAggregateType<T extends UsersAggregateArgs> = {
        [P in keyof T & keyof AggregateUsers]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateUsers[P]>
      : GetScalarType<T[P], AggregateUsers[P]>
  }




  export type usersGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: usersWhereInput
    orderBy?: usersOrderByWithAggregationInput | usersOrderByWithAggregationInput[]
    by: UsersScalarFieldEnum[] | UsersScalarFieldEnum
    having?: usersScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: UsersCountAggregateInputType | true
    _min?: UsersMinAggregateInputType
    _max?: UsersMaxAggregateInputType
  }

  export type UsersGroupByOutputType = {
    id: string
    email: string | null
    name: string | null
    password: string | null
    date_of_birth: Date | null
    gender: string | null
    allergies: string[]
    conditions: string[]
    is_admin: boolean | null
    phone_number: string | null
    emergency_contact: JsonValue | null
    created_at: Date | null
    updated_at: Date | null
    subscription_status: string | null
    subscription_plan: string | null
    subscription_expires_at: Date | null
    subscription_features: JsonValue | null
    _count: UsersCountAggregateOutputType | null
    _min: UsersMinAggregateOutputType | null
    _max: UsersMaxAggregateOutputType | null
  }

  type GetUsersGroupByPayload<T extends usersGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<UsersGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof UsersGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], UsersGroupByOutputType[P]>
            : GetScalarType<T[P], UsersGroupByOutputType[P]>
        }
      >
    >


  export type usersSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    name?: boolean
    password?: boolean
    date_of_birth?: boolean
    gender?: boolean
    allergies?: boolean
    conditions?: boolean
    is_admin?: boolean
    phone_number?: boolean
    emergency_contact?: boolean
    created_at?: boolean
    updated_at?: boolean
    subscription_status?: boolean
    subscription_plan?: boolean
    subscription_expires_at?: boolean
    subscription_features?: boolean
    adherence?: boolean | users$adherenceArgs<ExtArgs>
    medication?: boolean | users$medicationArgs<ExtArgs>
    reminders?: boolean | users$remindersArgs<ExtArgs>
    settings?: boolean | users$settingsArgs<ExtArgs>
    _count?: boolean | UsersCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["users"]>

  export type usersSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    name?: boolean
    password?: boolean
    date_of_birth?: boolean
    gender?: boolean
    allergies?: boolean
    conditions?: boolean
    is_admin?: boolean
    phone_number?: boolean
    emergency_contact?: boolean
    created_at?: boolean
    updated_at?: boolean
    subscription_status?: boolean
    subscription_plan?: boolean
    subscription_expires_at?: boolean
    subscription_features?: boolean
  }, ExtArgs["result"]["users"]>

  export type usersSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    name?: boolean
    password?: boolean
    date_of_birth?: boolean
    gender?: boolean
    allergies?: boolean
    conditions?: boolean
    is_admin?: boolean
    phone_number?: boolean
    emergency_contact?: boolean
    created_at?: boolean
    updated_at?: boolean
    subscription_status?: boolean
    subscription_plan?: boolean
    subscription_expires_at?: boolean
    subscription_features?: boolean
  }, ExtArgs["result"]["users"]>

  export type usersSelectScalar = {
    id?: boolean
    email?: boolean
    name?: boolean
    password?: boolean
    date_of_birth?: boolean
    gender?: boolean
    allergies?: boolean
    conditions?: boolean
    is_admin?: boolean
    phone_number?: boolean
    emergency_contact?: boolean
    created_at?: boolean
    updated_at?: boolean
    subscription_status?: boolean
    subscription_plan?: boolean
    subscription_expires_at?: boolean
    subscription_features?: boolean
  }

  export type usersOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "email" | "name" | "password" | "date_of_birth" | "gender" | "allergies" | "conditions" | "is_admin" | "phone_number" | "emergency_contact" | "created_at" | "updated_at" | "subscription_status" | "subscription_plan" | "subscription_expires_at" | "subscription_features", ExtArgs["result"]["users"]>
  export type usersInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    adherence?: boolean | users$adherenceArgs<ExtArgs>
    medication?: boolean | users$medicationArgs<ExtArgs>
    reminders?: boolean | users$remindersArgs<ExtArgs>
    settings?: boolean | users$settingsArgs<ExtArgs>
    _count?: boolean | UsersCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type usersIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}
  export type usersIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $usersPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "users"
    objects: {
      adherence: Prisma.$adherencePayload<ExtArgs>[]
      medication: Prisma.$medicationsPayload<ExtArgs>[]
      reminders: Prisma.$remindersPayload<ExtArgs>[]
      settings: Prisma.$user_settingsPayload<ExtArgs> | null
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      email: string | null
      name: string | null
      password: string | null
      date_of_birth: Date | null
      gender: string | null
      allergies: string[]
      conditions: string[]
      is_admin: boolean | null
      phone_number: string | null
      emergency_contact: Prisma.JsonValue | null
      created_at: Date | null
      updated_at: Date | null
      subscription_status: string | null
      subscription_plan: string | null
      subscription_expires_at: Date | null
      subscription_features: Prisma.JsonValue | null
    }, ExtArgs["result"]["users"]>
    composites: {}
  }

  type usersGetPayload<S extends boolean | null | undefined | usersDefaultArgs> = $Result.GetResult<Prisma.$usersPayload, S>

  type usersCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<usersFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: UsersCountAggregateInputType | true
    }

  export interface usersDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['users'], meta: { name: 'users' } }
    /**
     * Find zero or one Users that matches the filter.
     * @param {usersFindUniqueArgs} args - Arguments to find a Users
     * @example
     * // Get one Users
     * const users = await prisma.users.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends usersFindUniqueArgs>(args: SelectSubset<T, usersFindUniqueArgs<ExtArgs>>): Prisma__usersClient<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Users that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {usersFindUniqueOrThrowArgs} args - Arguments to find a Users
     * @example
     * // Get one Users
     * const users = await prisma.users.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends usersFindUniqueOrThrowArgs>(args: SelectSubset<T, usersFindUniqueOrThrowArgs<ExtArgs>>): Prisma__usersClient<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Users that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {usersFindFirstArgs} args - Arguments to find a Users
     * @example
     * // Get one Users
     * const users = await prisma.users.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends usersFindFirstArgs>(args?: SelectSubset<T, usersFindFirstArgs<ExtArgs>>): Prisma__usersClient<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Users that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {usersFindFirstOrThrowArgs} args - Arguments to find a Users
     * @example
     * // Get one Users
     * const users = await prisma.users.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends usersFindFirstOrThrowArgs>(args?: SelectSubset<T, usersFindFirstOrThrowArgs<ExtArgs>>): Prisma__usersClient<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Users that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {usersFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Users
     * const users = await prisma.users.findMany()
     * 
     * // Get first 10 Users
     * const users = await prisma.users.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const usersWithIdOnly = await prisma.users.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends usersFindManyArgs>(args?: SelectSubset<T, usersFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Users.
     * @param {usersCreateArgs} args - Arguments to create a Users.
     * @example
     * // Create one Users
     * const Users = await prisma.users.create({
     *   data: {
     *     // ... data to create a Users
     *   }
     * })
     * 
     */
    create<T extends usersCreateArgs>(args: SelectSubset<T, usersCreateArgs<ExtArgs>>): Prisma__usersClient<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Users.
     * @param {usersCreateManyArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const users = await prisma.users.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends usersCreateManyArgs>(args?: SelectSubset<T, usersCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Users and returns the data saved in the database.
     * @param {usersCreateManyAndReturnArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const users = await prisma.users.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Users and only return the `id`
     * const usersWithIdOnly = await prisma.users.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends usersCreateManyAndReturnArgs>(args?: SelectSubset<T, usersCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Users.
     * @param {usersDeleteArgs} args - Arguments to delete one Users.
     * @example
     * // Delete one Users
     * const Users = await prisma.users.delete({
     *   where: {
     *     // ... filter to delete one Users
     *   }
     * })
     * 
     */
    delete<T extends usersDeleteArgs>(args: SelectSubset<T, usersDeleteArgs<ExtArgs>>): Prisma__usersClient<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Users.
     * @param {usersUpdateArgs} args - Arguments to update one Users.
     * @example
     * // Update one Users
     * const users = await prisma.users.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends usersUpdateArgs>(args: SelectSubset<T, usersUpdateArgs<ExtArgs>>): Prisma__usersClient<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Users.
     * @param {usersDeleteManyArgs} args - Arguments to filter Users to delete.
     * @example
     * // Delete a few Users
     * const { count } = await prisma.users.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends usersDeleteManyArgs>(args?: SelectSubset<T, usersDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {usersUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Users
     * const users = await prisma.users.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends usersUpdateManyArgs>(args: SelectSubset<T, usersUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users and returns the data updated in the database.
     * @param {usersUpdateManyAndReturnArgs} args - Arguments to update many Users.
     * @example
     * // Update many Users
     * const users = await prisma.users.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Users and only return the `id`
     * const usersWithIdOnly = await prisma.users.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends usersUpdateManyAndReturnArgs>(args: SelectSubset<T, usersUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Users.
     * @param {usersUpsertArgs} args - Arguments to update or create a Users.
     * @example
     * // Update or create a Users
     * const users = await prisma.users.upsert({
     *   create: {
     *     // ... data to create a Users
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Users we want to update
     *   }
     * })
     */
    upsert<T extends usersUpsertArgs>(args: SelectSubset<T, usersUpsertArgs<ExtArgs>>): Prisma__usersClient<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {usersCountArgs} args - Arguments to filter Users to count.
     * @example
     * // Count the number of Users
     * const count = await prisma.users.count({
     *   where: {
     *     // ... the filter for the Users we want to count
     *   }
     * })
    **/
    count<T extends usersCountArgs>(
      args?: Subset<T, usersCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], UsersCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UsersAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends UsersAggregateArgs>(args: Subset<T, UsersAggregateArgs>): Prisma.PrismaPromise<GetUsersAggregateType<T>>

    /**
     * Group by Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {usersGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends usersGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: usersGroupByArgs['orderBy'] }
        : { orderBy?: usersGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, usersGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUsersGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the users model
   */
  readonly fields: usersFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for users.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__usersClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    adherence<T extends users$adherenceArgs<ExtArgs> = {}>(args?: Subset<T, users$adherenceArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$adherencePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    medication<T extends users$medicationArgs<ExtArgs> = {}>(args?: Subset<T, users$medicationArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$medicationsPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    reminders<T extends users$remindersArgs<ExtArgs> = {}>(args?: Subset<T, users$remindersArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$remindersPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    settings<T extends users$settingsArgs<ExtArgs> = {}>(args?: Subset<T, users$settingsArgs<ExtArgs>>): Prisma__user_settingsClient<$Result.GetResult<Prisma.$user_settingsPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the users model
   */
  interface usersFieldRefs {
    readonly id: FieldRef<"users", 'String'>
    readonly email: FieldRef<"users", 'String'>
    readonly name: FieldRef<"users", 'String'>
    readonly password: FieldRef<"users", 'String'>
    readonly date_of_birth: FieldRef<"users", 'DateTime'>
    readonly gender: FieldRef<"users", 'String'>
    readonly allergies: FieldRef<"users", 'String[]'>
    readonly conditions: FieldRef<"users", 'String[]'>
    readonly is_admin: FieldRef<"users", 'Boolean'>
    readonly phone_number: FieldRef<"users", 'String'>
    readonly emergency_contact: FieldRef<"users", 'Json'>
    readonly created_at: FieldRef<"users", 'DateTime'>
    readonly updated_at: FieldRef<"users", 'DateTime'>
    readonly subscription_status: FieldRef<"users", 'String'>
    readonly subscription_plan: FieldRef<"users", 'String'>
    readonly subscription_expires_at: FieldRef<"users", 'DateTime'>
    readonly subscription_features: FieldRef<"users", 'Json'>
  }
    

  // Custom InputTypes
  /**
   * users findUnique
   */
  export type usersFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the users
     */
    select?: usersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the users
     */
    omit?: usersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: usersInclude<ExtArgs> | null
    /**
     * Filter, which users to fetch.
     */
    where: usersWhereUniqueInput
  }

  /**
   * users findUniqueOrThrow
   */
  export type usersFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the users
     */
    select?: usersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the users
     */
    omit?: usersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: usersInclude<ExtArgs> | null
    /**
     * Filter, which users to fetch.
     */
    where: usersWhereUniqueInput
  }

  /**
   * users findFirst
   */
  export type usersFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the users
     */
    select?: usersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the users
     */
    omit?: usersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: usersInclude<ExtArgs> | null
    /**
     * Filter, which users to fetch.
     */
    where?: usersWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of users to fetch.
     */
    orderBy?: usersOrderByWithRelationInput | usersOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for users.
     */
    cursor?: usersWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of users.
     */
    distinct?: UsersScalarFieldEnum | UsersScalarFieldEnum[]
  }

  /**
   * users findFirstOrThrow
   */
  export type usersFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the users
     */
    select?: usersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the users
     */
    omit?: usersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: usersInclude<ExtArgs> | null
    /**
     * Filter, which users to fetch.
     */
    where?: usersWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of users to fetch.
     */
    orderBy?: usersOrderByWithRelationInput | usersOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for users.
     */
    cursor?: usersWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of users.
     */
    distinct?: UsersScalarFieldEnum | UsersScalarFieldEnum[]
  }

  /**
   * users findMany
   */
  export type usersFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the users
     */
    select?: usersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the users
     */
    omit?: usersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: usersInclude<ExtArgs> | null
    /**
     * Filter, which users to fetch.
     */
    where?: usersWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of users to fetch.
     */
    orderBy?: usersOrderByWithRelationInput | usersOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing users.
     */
    cursor?: usersWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` users.
     */
    skip?: number
    distinct?: UsersScalarFieldEnum | UsersScalarFieldEnum[]
  }

  /**
   * users create
   */
  export type usersCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the users
     */
    select?: usersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the users
     */
    omit?: usersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: usersInclude<ExtArgs> | null
    /**
     * The data needed to create a users.
     */
    data?: XOR<usersCreateInput, usersUncheckedCreateInput>
  }

  /**
   * users createMany
   */
  export type usersCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many users.
     */
    data: usersCreateManyInput | usersCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * users createManyAndReturn
   */
  export type usersCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the users
     */
    select?: usersSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the users
     */
    omit?: usersOmit<ExtArgs> | null
    /**
     * The data used to create many users.
     */
    data: usersCreateManyInput | usersCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * users update
   */
  export type usersUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the users
     */
    select?: usersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the users
     */
    omit?: usersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: usersInclude<ExtArgs> | null
    /**
     * The data needed to update a users.
     */
    data: XOR<usersUpdateInput, usersUncheckedUpdateInput>
    /**
     * Choose, which users to update.
     */
    where: usersWhereUniqueInput
  }

  /**
   * users updateMany
   */
  export type usersUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update users.
     */
    data: XOR<usersUpdateManyMutationInput, usersUncheckedUpdateManyInput>
    /**
     * Filter which users to update
     */
    where?: usersWhereInput
    /**
     * Limit how many users to update.
     */
    limit?: number
  }

  /**
   * users updateManyAndReturn
   */
  export type usersUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the users
     */
    select?: usersSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the users
     */
    omit?: usersOmit<ExtArgs> | null
    /**
     * The data used to update users.
     */
    data: XOR<usersUpdateManyMutationInput, usersUncheckedUpdateManyInput>
    /**
     * Filter which users to update
     */
    where?: usersWhereInput
    /**
     * Limit how many users to update.
     */
    limit?: number
  }

  /**
   * users upsert
   */
  export type usersUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the users
     */
    select?: usersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the users
     */
    omit?: usersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: usersInclude<ExtArgs> | null
    /**
     * The filter to search for the users to update in case it exists.
     */
    where: usersWhereUniqueInput
    /**
     * In case the users found by the `where` argument doesn't exist, create a new users with this data.
     */
    create: XOR<usersCreateInput, usersUncheckedCreateInput>
    /**
     * In case the users was found with the provided `where` argument, update it with this data.
     */
    update: XOR<usersUpdateInput, usersUncheckedUpdateInput>
  }

  /**
   * users delete
   */
  export type usersDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the users
     */
    select?: usersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the users
     */
    omit?: usersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: usersInclude<ExtArgs> | null
    /**
     * Filter which users to delete.
     */
    where: usersWhereUniqueInput
  }

  /**
   * users deleteMany
   */
  export type usersDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which users to delete
     */
    where?: usersWhereInput
    /**
     * Limit how many users to delete.
     */
    limit?: number
  }

  /**
   * users.adherence
   */
  export type users$adherenceArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the adherence
     */
    select?: adherenceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the adherence
     */
    omit?: adherenceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: adherenceInclude<ExtArgs> | null
    where?: adherenceWhereInput
    orderBy?: adherenceOrderByWithRelationInput | adherenceOrderByWithRelationInput[]
    cursor?: adherenceWhereUniqueInput
    take?: number
    skip?: number
    distinct?: AdherenceScalarFieldEnum | AdherenceScalarFieldEnum[]
  }

  /**
   * users.medication
   */
  export type users$medicationArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the medications
     */
    select?: medicationsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the medications
     */
    omit?: medicationsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: medicationsInclude<ExtArgs> | null
    where?: medicationsWhereInput
    orderBy?: medicationsOrderByWithRelationInput | medicationsOrderByWithRelationInput[]
    cursor?: medicationsWhereUniqueInput
    take?: number
    skip?: number
    distinct?: MedicationsScalarFieldEnum | MedicationsScalarFieldEnum[]
  }

  /**
   * users.reminders
   */
  export type users$remindersArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the reminders
     */
    select?: remindersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the reminders
     */
    omit?: remindersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: remindersInclude<ExtArgs> | null
    where?: remindersWhereInput
    orderBy?: remindersOrderByWithRelationInput | remindersOrderByWithRelationInput[]
    cursor?: remindersWhereUniqueInput
    take?: number
    skip?: number
    distinct?: RemindersScalarFieldEnum | RemindersScalarFieldEnum[]
  }

  /**
   * users.settings
   */
  export type users$settingsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the user_settings
     */
    select?: user_settingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the user_settings
     */
    omit?: user_settingsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: user_settingsInclude<ExtArgs> | null
    where?: user_settingsWhereInput
  }

  /**
   * users without action
   */
  export type usersDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the users
     */
    select?: usersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the users
     */
    omit?: usersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: usersInclude<ExtArgs> | null
  }


  /**
   * Model user_settings
   */

  export type AggregateUser_settings = {
    _count: User_settingsCountAggregateOutputType | null
    _min: User_settingsMinAggregateOutputType | null
    _max: User_settingsMaxAggregateOutputType | null
  }

  export type User_settingsMinAggregateOutputType = {
    id: string | null
    user_id: string | null
    email_enabled: boolean | null
    timezone: string | null
    created_at: Date | null
    updated_at: Date | null
  }

  export type User_settingsMaxAggregateOutputType = {
    id: string | null
    user_id: string | null
    email_enabled: boolean | null
    timezone: string | null
    created_at: Date | null
    updated_at: Date | null
  }

  export type User_settingsCountAggregateOutputType = {
    id: number
    user_id: number
    email_enabled: number
    preferred_times: number
    timezone: number
    notification_preferences: number
    created_at: number
    updated_at: number
    _all: number
  }


  export type User_settingsMinAggregateInputType = {
    id?: true
    user_id?: true
    email_enabled?: true
    timezone?: true
    created_at?: true
    updated_at?: true
  }

  export type User_settingsMaxAggregateInputType = {
    id?: true
    user_id?: true
    email_enabled?: true
    timezone?: true
    created_at?: true
    updated_at?: true
  }

  export type User_settingsCountAggregateInputType = {
    id?: true
    user_id?: true
    email_enabled?: true
    preferred_times?: true
    timezone?: true
    notification_preferences?: true
    created_at?: true
    updated_at?: true
    _all?: true
  }

  export type User_settingsAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which user_settings to aggregate.
     */
    where?: user_settingsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of user_settings to fetch.
     */
    orderBy?: user_settingsOrderByWithRelationInput | user_settingsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: user_settingsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` user_settings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` user_settings.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned user_settings
    **/
    _count?: true | User_settingsCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: User_settingsMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: User_settingsMaxAggregateInputType
  }

  export type GetUser_settingsAggregateType<T extends User_settingsAggregateArgs> = {
        [P in keyof T & keyof AggregateUser_settings]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateUser_settings[P]>
      : GetScalarType<T[P], AggregateUser_settings[P]>
  }




  export type user_settingsGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: user_settingsWhereInput
    orderBy?: user_settingsOrderByWithAggregationInput | user_settingsOrderByWithAggregationInput[]
    by: User_settingsScalarFieldEnum[] | User_settingsScalarFieldEnum
    having?: user_settingsScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: User_settingsCountAggregateInputType | true
    _min?: User_settingsMinAggregateInputType
    _max?: User_settingsMaxAggregateInputType
  }

  export type User_settingsGroupByOutputType = {
    id: string
    user_id: string
    email_enabled: boolean
    preferred_times: string[]
    timezone: string
    notification_preferences: JsonValue | null
    created_at: Date | null
    updated_at: Date | null
    _count: User_settingsCountAggregateOutputType | null
    _min: User_settingsMinAggregateOutputType | null
    _max: User_settingsMaxAggregateOutputType | null
  }

  type GetUser_settingsGroupByPayload<T extends user_settingsGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<User_settingsGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof User_settingsGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], User_settingsGroupByOutputType[P]>
            : GetScalarType<T[P], User_settingsGroupByOutputType[P]>
        }
      >
    >


  export type user_settingsSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    user_id?: boolean
    email_enabled?: boolean
    preferred_times?: boolean
    timezone?: boolean
    notification_preferences?: boolean
    created_at?: boolean
    updated_at?: boolean
    user?: boolean | usersDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["user_settings"]>

  export type user_settingsSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    user_id?: boolean
    email_enabled?: boolean
    preferred_times?: boolean
    timezone?: boolean
    notification_preferences?: boolean
    created_at?: boolean
    updated_at?: boolean
    user?: boolean | usersDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["user_settings"]>

  export type user_settingsSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    user_id?: boolean
    email_enabled?: boolean
    preferred_times?: boolean
    timezone?: boolean
    notification_preferences?: boolean
    created_at?: boolean
    updated_at?: boolean
    user?: boolean | usersDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["user_settings"]>

  export type user_settingsSelectScalar = {
    id?: boolean
    user_id?: boolean
    email_enabled?: boolean
    preferred_times?: boolean
    timezone?: boolean
    notification_preferences?: boolean
    created_at?: boolean
    updated_at?: boolean
  }

  export type user_settingsOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "user_id" | "email_enabled" | "preferred_times" | "timezone" | "notification_preferences" | "created_at" | "updated_at", ExtArgs["result"]["user_settings"]>
  export type user_settingsInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | usersDefaultArgs<ExtArgs>
  }
  export type user_settingsIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | usersDefaultArgs<ExtArgs>
  }
  export type user_settingsIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | usersDefaultArgs<ExtArgs>
  }

  export type $user_settingsPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "user_settings"
    objects: {
      user: Prisma.$usersPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      user_id: string
      email_enabled: boolean
      preferred_times: string[]
      timezone: string
      notification_preferences: Prisma.JsonValue | null
      created_at: Date | null
      updated_at: Date | null
    }, ExtArgs["result"]["user_settings"]>
    composites: {}
  }

  type user_settingsGetPayload<S extends boolean | null | undefined | user_settingsDefaultArgs> = $Result.GetResult<Prisma.$user_settingsPayload, S>

  type user_settingsCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<user_settingsFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: User_settingsCountAggregateInputType | true
    }

  export interface user_settingsDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['user_settings'], meta: { name: 'user_settings' } }
    /**
     * Find zero or one User_settings that matches the filter.
     * @param {user_settingsFindUniqueArgs} args - Arguments to find a User_settings
     * @example
     * // Get one User_settings
     * const user_settings = await prisma.user_settings.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends user_settingsFindUniqueArgs>(args: SelectSubset<T, user_settingsFindUniqueArgs<ExtArgs>>): Prisma__user_settingsClient<$Result.GetResult<Prisma.$user_settingsPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one User_settings that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {user_settingsFindUniqueOrThrowArgs} args - Arguments to find a User_settings
     * @example
     * // Get one User_settings
     * const user_settings = await prisma.user_settings.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends user_settingsFindUniqueOrThrowArgs>(args: SelectSubset<T, user_settingsFindUniqueOrThrowArgs<ExtArgs>>): Prisma__user_settingsClient<$Result.GetResult<Prisma.$user_settingsPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first User_settings that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {user_settingsFindFirstArgs} args - Arguments to find a User_settings
     * @example
     * // Get one User_settings
     * const user_settings = await prisma.user_settings.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends user_settingsFindFirstArgs>(args?: SelectSubset<T, user_settingsFindFirstArgs<ExtArgs>>): Prisma__user_settingsClient<$Result.GetResult<Prisma.$user_settingsPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first User_settings that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {user_settingsFindFirstOrThrowArgs} args - Arguments to find a User_settings
     * @example
     * // Get one User_settings
     * const user_settings = await prisma.user_settings.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends user_settingsFindFirstOrThrowArgs>(args?: SelectSubset<T, user_settingsFindFirstOrThrowArgs<ExtArgs>>): Prisma__user_settingsClient<$Result.GetResult<Prisma.$user_settingsPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more User_settings that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {user_settingsFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all User_settings
     * const user_settings = await prisma.user_settings.findMany()
     * 
     * // Get first 10 User_settings
     * const user_settings = await prisma.user_settings.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const user_settingsWithIdOnly = await prisma.user_settings.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends user_settingsFindManyArgs>(args?: SelectSubset<T, user_settingsFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$user_settingsPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a User_settings.
     * @param {user_settingsCreateArgs} args - Arguments to create a User_settings.
     * @example
     * // Create one User_settings
     * const User_settings = await prisma.user_settings.create({
     *   data: {
     *     // ... data to create a User_settings
     *   }
     * })
     * 
     */
    create<T extends user_settingsCreateArgs>(args: SelectSubset<T, user_settingsCreateArgs<ExtArgs>>): Prisma__user_settingsClient<$Result.GetResult<Prisma.$user_settingsPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many User_settings.
     * @param {user_settingsCreateManyArgs} args - Arguments to create many User_settings.
     * @example
     * // Create many User_settings
     * const user_settings = await prisma.user_settings.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends user_settingsCreateManyArgs>(args?: SelectSubset<T, user_settingsCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many User_settings and returns the data saved in the database.
     * @param {user_settingsCreateManyAndReturnArgs} args - Arguments to create many User_settings.
     * @example
     * // Create many User_settings
     * const user_settings = await prisma.user_settings.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many User_settings and only return the `id`
     * const user_settingsWithIdOnly = await prisma.user_settings.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends user_settingsCreateManyAndReturnArgs>(args?: SelectSubset<T, user_settingsCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$user_settingsPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a User_settings.
     * @param {user_settingsDeleteArgs} args - Arguments to delete one User_settings.
     * @example
     * // Delete one User_settings
     * const User_settings = await prisma.user_settings.delete({
     *   where: {
     *     // ... filter to delete one User_settings
     *   }
     * })
     * 
     */
    delete<T extends user_settingsDeleteArgs>(args: SelectSubset<T, user_settingsDeleteArgs<ExtArgs>>): Prisma__user_settingsClient<$Result.GetResult<Prisma.$user_settingsPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one User_settings.
     * @param {user_settingsUpdateArgs} args - Arguments to update one User_settings.
     * @example
     * // Update one User_settings
     * const user_settings = await prisma.user_settings.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends user_settingsUpdateArgs>(args: SelectSubset<T, user_settingsUpdateArgs<ExtArgs>>): Prisma__user_settingsClient<$Result.GetResult<Prisma.$user_settingsPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more User_settings.
     * @param {user_settingsDeleteManyArgs} args - Arguments to filter User_settings to delete.
     * @example
     * // Delete a few User_settings
     * const { count } = await prisma.user_settings.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends user_settingsDeleteManyArgs>(args?: SelectSubset<T, user_settingsDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more User_settings.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {user_settingsUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many User_settings
     * const user_settings = await prisma.user_settings.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends user_settingsUpdateManyArgs>(args: SelectSubset<T, user_settingsUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more User_settings and returns the data updated in the database.
     * @param {user_settingsUpdateManyAndReturnArgs} args - Arguments to update many User_settings.
     * @example
     * // Update many User_settings
     * const user_settings = await prisma.user_settings.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more User_settings and only return the `id`
     * const user_settingsWithIdOnly = await prisma.user_settings.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends user_settingsUpdateManyAndReturnArgs>(args: SelectSubset<T, user_settingsUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$user_settingsPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one User_settings.
     * @param {user_settingsUpsertArgs} args - Arguments to update or create a User_settings.
     * @example
     * // Update or create a User_settings
     * const user_settings = await prisma.user_settings.upsert({
     *   create: {
     *     // ... data to create a User_settings
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the User_settings we want to update
     *   }
     * })
     */
    upsert<T extends user_settingsUpsertArgs>(args: SelectSubset<T, user_settingsUpsertArgs<ExtArgs>>): Prisma__user_settingsClient<$Result.GetResult<Prisma.$user_settingsPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of User_settings.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {user_settingsCountArgs} args - Arguments to filter User_settings to count.
     * @example
     * // Count the number of User_settings
     * const count = await prisma.user_settings.count({
     *   where: {
     *     // ... the filter for the User_settings we want to count
     *   }
     * })
    **/
    count<T extends user_settingsCountArgs>(
      args?: Subset<T, user_settingsCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], User_settingsCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a User_settings.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {User_settingsAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends User_settingsAggregateArgs>(args: Subset<T, User_settingsAggregateArgs>): Prisma.PrismaPromise<GetUser_settingsAggregateType<T>>

    /**
     * Group by User_settings.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {user_settingsGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends user_settingsGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: user_settingsGroupByArgs['orderBy'] }
        : { orderBy?: user_settingsGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, user_settingsGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUser_settingsGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the user_settings model
   */
  readonly fields: user_settingsFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for user_settings.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__user_settingsClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends usersDefaultArgs<ExtArgs> = {}>(args?: Subset<T, usersDefaultArgs<ExtArgs>>): Prisma__usersClient<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the user_settings model
   */
  interface user_settingsFieldRefs {
    readonly id: FieldRef<"user_settings", 'String'>
    readonly user_id: FieldRef<"user_settings", 'String'>
    readonly email_enabled: FieldRef<"user_settings", 'Boolean'>
    readonly preferred_times: FieldRef<"user_settings", 'String[]'>
    readonly timezone: FieldRef<"user_settings", 'String'>
    readonly notification_preferences: FieldRef<"user_settings", 'Json'>
    readonly created_at: FieldRef<"user_settings", 'DateTime'>
    readonly updated_at: FieldRef<"user_settings", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * user_settings findUnique
   */
  export type user_settingsFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the user_settings
     */
    select?: user_settingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the user_settings
     */
    omit?: user_settingsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: user_settingsInclude<ExtArgs> | null
    /**
     * Filter, which user_settings to fetch.
     */
    where: user_settingsWhereUniqueInput
  }

  /**
   * user_settings findUniqueOrThrow
   */
  export type user_settingsFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the user_settings
     */
    select?: user_settingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the user_settings
     */
    omit?: user_settingsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: user_settingsInclude<ExtArgs> | null
    /**
     * Filter, which user_settings to fetch.
     */
    where: user_settingsWhereUniqueInput
  }

  /**
   * user_settings findFirst
   */
  export type user_settingsFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the user_settings
     */
    select?: user_settingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the user_settings
     */
    omit?: user_settingsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: user_settingsInclude<ExtArgs> | null
    /**
     * Filter, which user_settings to fetch.
     */
    where?: user_settingsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of user_settings to fetch.
     */
    orderBy?: user_settingsOrderByWithRelationInput | user_settingsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for user_settings.
     */
    cursor?: user_settingsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` user_settings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` user_settings.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of user_settings.
     */
    distinct?: User_settingsScalarFieldEnum | User_settingsScalarFieldEnum[]
  }

  /**
   * user_settings findFirstOrThrow
   */
  export type user_settingsFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the user_settings
     */
    select?: user_settingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the user_settings
     */
    omit?: user_settingsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: user_settingsInclude<ExtArgs> | null
    /**
     * Filter, which user_settings to fetch.
     */
    where?: user_settingsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of user_settings to fetch.
     */
    orderBy?: user_settingsOrderByWithRelationInput | user_settingsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for user_settings.
     */
    cursor?: user_settingsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` user_settings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` user_settings.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of user_settings.
     */
    distinct?: User_settingsScalarFieldEnum | User_settingsScalarFieldEnum[]
  }

  /**
   * user_settings findMany
   */
  export type user_settingsFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the user_settings
     */
    select?: user_settingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the user_settings
     */
    omit?: user_settingsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: user_settingsInclude<ExtArgs> | null
    /**
     * Filter, which user_settings to fetch.
     */
    where?: user_settingsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of user_settings to fetch.
     */
    orderBy?: user_settingsOrderByWithRelationInput | user_settingsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing user_settings.
     */
    cursor?: user_settingsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` user_settings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` user_settings.
     */
    skip?: number
    distinct?: User_settingsScalarFieldEnum | User_settingsScalarFieldEnum[]
  }

  /**
   * user_settings create
   */
  export type user_settingsCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the user_settings
     */
    select?: user_settingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the user_settings
     */
    omit?: user_settingsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: user_settingsInclude<ExtArgs> | null
    /**
     * The data needed to create a user_settings.
     */
    data: XOR<user_settingsCreateInput, user_settingsUncheckedCreateInput>
  }

  /**
   * user_settings createMany
   */
  export type user_settingsCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many user_settings.
     */
    data: user_settingsCreateManyInput | user_settingsCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * user_settings createManyAndReturn
   */
  export type user_settingsCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the user_settings
     */
    select?: user_settingsSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the user_settings
     */
    omit?: user_settingsOmit<ExtArgs> | null
    /**
     * The data used to create many user_settings.
     */
    data: user_settingsCreateManyInput | user_settingsCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: user_settingsIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * user_settings update
   */
  export type user_settingsUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the user_settings
     */
    select?: user_settingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the user_settings
     */
    omit?: user_settingsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: user_settingsInclude<ExtArgs> | null
    /**
     * The data needed to update a user_settings.
     */
    data: XOR<user_settingsUpdateInput, user_settingsUncheckedUpdateInput>
    /**
     * Choose, which user_settings to update.
     */
    where: user_settingsWhereUniqueInput
  }

  /**
   * user_settings updateMany
   */
  export type user_settingsUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update user_settings.
     */
    data: XOR<user_settingsUpdateManyMutationInput, user_settingsUncheckedUpdateManyInput>
    /**
     * Filter which user_settings to update
     */
    where?: user_settingsWhereInput
    /**
     * Limit how many user_settings to update.
     */
    limit?: number
  }

  /**
   * user_settings updateManyAndReturn
   */
  export type user_settingsUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the user_settings
     */
    select?: user_settingsSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the user_settings
     */
    omit?: user_settingsOmit<ExtArgs> | null
    /**
     * The data used to update user_settings.
     */
    data: XOR<user_settingsUpdateManyMutationInput, user_settingsUncheckedUpdateManyInput>
    /**
     * Filter which user_settings to update
     */
    where?: user_settingsWhereInput
    /**
     * Limit how many user_settings to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: user_settingsIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * user_settings upsert
   */
  export type user_settingsUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the user_settings
     */
    select?: user_settingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the user_settings
     */
    omit?: user_settingsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: user_settingsInclude<ExtArgs> | null
    /**
     * The filter to search for the user_settings to update in case it exists.
     */
    where: user_settingsWhereUniqueInput
    /**
     * In case the user_settings found by the `where` argument doesn't exist, create a new user_settings with this data.
     */
    create: XOR<user_settingsCreateInput, user_settingsUncheckedCreateInput>
    /**
     * In case the user_settings was found with the provided `where` argument, update it with this data.
     */
    update: XOR<user_settingsUpdateInput, user_settingsUncheckedUpdateInput>
  }

  /**
   * user_settings delete
   */
  export type user_settingsDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the user_settings
     */
    select?: user_settingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the user_settings
     */
    omit?: user_settingsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: user_settingsInclude<ExtArgs> | null
    /**
     * Filter which user_settings to delete.
     */
    where: user_settingsWhereUniqueInput
  }

  /**
   * user_settings deleteMany
   */
  export type user_settingsDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which user_settings to delete
     */
    where?: user_settingsWhereInput
    /**
     * Limit how many user_settings to delete.
     */
    limit?: number
  }

  /**
   * user_settings without action
   */
  export type user_settingsDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the user_settings
     */
    select?: user_settingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the user_settings
     */
    omit?: user_settingsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: user_settingsInclude<ExtArgs> | null
  }


  /**
   * Model medications
   */

  export type AggregateMedications = {
    _count: MedicationsCountAggregateOutputType | null
    _min: MedicationsMinAggregateOutputType | null
    _max: MedicationsMaxAggregateOutputType | null
  }

  export type MedicationsMinAggregateOutputType = {
    id: string | null
    user_id: string | null
    name: string | null
    instructions: string | null
    start_date: Date | null
    end_date: Date | null
    active: boolean | null
    medication_type: string | null
    image_url: string | null
    created_at: Date | null
    updated_at: Date | null
  }

  export type MedicationsMaxAggregateOutputType = {
    id: string | null
    user_id: string | null
    name: string | null
    instructions: string | null
    start_date: Date | null
    end_date: Date | null
    active: boolean | null
    medication_type: string | null
    image_url: string | null
    created_at: Date | null
    updated_at: Date | null
  }

  export type MedicationsCountAggregateOutputType = {
    id: number
    user_id: number
    name: number
    dosage: number
    frequency: number
    scheduled_times: number
    instructions: number
    start_date: number
    end_date: number
    refill_reminder: number
    side_effects_to_watch: number
    active: number
    medication_type: number
    image_url: number
    created_at: number
    updated_at: number
    _all: number
  }


  export type MedicationsMinAggregateInputType = {
    id?: true
    user_id?: true
    name?: true
    instructions?: true
    start_date?: true
    end_date?: true
    active?: true
    medication_type?: true
    image_url?: true
    created_at?: true
    updated_at?: true
  }

  export type MedicationsMaxAggregateInputType = {
    id?: true
    user_id?: true
    name?: true
    instructions?: true
    start_date?: true
    end_date?: true
    active?: true
    medication_type?: true
    image_url?: true
    created_at?: true
    updated_at?: true
  }

  export type MedicationsCountAggregateInputType = {
    id?: true
    user_id?: true
    name?: true
    dosage?: true
    frequency?: true
    scheduled_times?: true
    instructions?: true
    start_date?: true
    end_date?: true
    refill_reminder?: true
    side_effects_to_watch?: true
    active?: true
    medication_type?: true
    image_url?: true
    created_at?: true
    updated_at?: true
    _all?: true
  }

  export type MedicationsAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which medications to aggregate.
     */
    where?: medicationsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of medications to fetch.
     */
    orderBy?: medicationsOrderByWithRelationInput | medicationsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: medicationsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` medications from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` medications.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned medications
    **/
    _count?: true | MedicationsCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: MedicationsMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: MedicationsMaxAggregateInputType
  }

  export type GetMedicationsAggregateType<T extends MedicationsAggregateArgs> = {
        [P in keyof T & keyof AggregateMedications]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateMedications[P]>
      : GetScalarType<T[P], AggregateMedications[P]>
  }




  export type medicationsGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: medicationsWhereInput
    orderBy?: medicationsOrderByWithAggregationInput | medicationsOrderByWithAggregationInput[]
    by: MedicationsScalarFieldEnum[] | MedicationsScalarFieldEnum
    having?: medicationsScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: MedicationsCountAggregateInputType | true
    _min?: MedicationsMinAggregateInputType
    _max?: MedicationsMaxAggregateInputType
  }

  export type MedicationsGroupByOutputType = {
    id: string
    user_id: string
    name: string
    dosage: JsonValue
    frequency: JsonValue
    scheduled_times: string[]
    instructions: string | null
    start_date: Date | null
    end_date: Date | null
    refill_reminder: JsonValue | null
    side_effects_to_watch: string[]
    active: boolean | null
    medication_type: string | null
    image_url: string | null
    created_at: Date | null
    updated_at: Date | null
    _count: MedicationsCountAggregateOutputType | null
    _min: MedicationsMinAggregateOutputType | null
    _max: MedicationsMaxAggregateOutputType | null
  }

  type GetMedicationsGroupByPayload<T extends medicationsGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<MedicationsGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof MedicationsGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], MedicationsGroupByOutputType[P]>
            : GetScalarType<T[P], MedicationsGroupByOutputType[P]>
        }
      >
    >


  export type medicationsSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    user_id?: boolean
    name?: boolean
    dosage?: boolean
    frequency?: boolean
    scheduled_times?: boolean
    instructions?: boolean
    start_date?: boolean
    end_date?: boolean
    refill_reminder?: boolean
    side_effects_to_watch?: boolean
    active?: boolean
    medication_type?: boolean
    image_url?: boolean
    created_at?: boolean
    updated_at?: boolean
    adherence?: boolean | medications$adherenceArgs<ExtArgs>
    user?: boolean | usersDefaultArgs<ExtArgs>
    reminders?: boolean | medications$remindersArgs<ExtArgs>
    _count?: boolean | MedicationsCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["medications"]>

  export type medicationsSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    user_id?: boolean
    name?: boolean
    dosage?: boolean
    frequency?: boolean
    scheduled_times?: boolean
    instructions?: boolean
    start_date?: boolean
    end_date?: boolean
    refill_reminder?: boolean
    side_effects_to_watch?: boolean
    active?: boolean
    medication_type?: boolean
    image_url?: boolean
    created_at?: boolean
    updated_at?: boolean
    user?: boolean | usersDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["medications"]>

  export type medicationsSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    user_id?: boolean
    name?: boolean
    dosage?: boolean
    frequency?: boolean
    scheduled_times?: boolean
    instructions?: boolean
    start_date?: boolean
    end_date?: boolean
    refill_reminder?: boolean
    side_effects_to_watch?: boolean
    active?: boolean
    medication_type?: boolean
    image_url?: boolean
    created_at?: boolean
    updated_at?: boolean
    user?: boolean | usersDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["medications"]>

  export type medicationsSelectScalar = {
    id?: boolean
    user_id?: boolean
    name?: boolean
    dosage?: boolean
    frequency?: boolean
    scheduled_times?: boolean
    instructions?: boolean
    start_date?: boolean
    end_date?: boolean
    refill_reminder?: boolean
    side_effects_to_watch?: boolean
    active?: boolean
    medication_type?: boolean
    image_url?: boolean
    created_at?: boolean
    updated_at?: boolean
  }

  export type medicationsOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "user_id" | "name" | "dosage" | "frequency" | "scheduled_times" | "instructions" | "start_date" | "end_date" | "refill_reminder" | "side_effects_to_watch" | "active" | "medication_type" | "image_url" | "created_at" | "updated_at", ExtArgs["result"]["medications"]>
  export type medicationsInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    adherence?: boolean | medications$adherenceArgs<ExtArgs>
    user?: boolean | usersDefaultArgs<ExtArgs>
    reminders?: boolean | medications$remindersArgs<ExtArgs>
    _count?: boolean | MedicationsCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type medicationsIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | usersDefaultArgs<ExtArgs>
  }
  export type medicationsIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | usersDefaultArgs<ExtArgs>
  }

  export type $medicationsPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "medications"
    objects: {
      adherence: Prisma.$adherencePayload<ExtArgs>[]
      user: Prisma.$usersPayload<ExtArgs>
      reminders: Prisma.$remindersPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      user_id: string
      name: string
      dosage: Prisma.JsonValue
      frequency: Prisma.JsonValue
      scheduled_times: string[]
      instructions: string | null
      start_date: Date | null
      end_date: Date | null
      refill_reminder: Prisma.JsonValue | null
      side_effects_to_watch: string[]
      active: boolean | null
      medication_type: string | null
      image_url: string | null
      created_at: Date | null
      updated_at: Date | null
    }, ExtArgs["result"]["medications"]>
    composites: {}
  }

  type medicationsGetPayload<S extends boolean | null | undefined | medicationsDefaultArgs> = $Result.GetResult<Prisma.$medicationsPayload, S>

  type medicationsCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<medicationsFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: MedicationsCountAggregateInputType | true
    }

  export interface medicationsDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['medications'], meta: { name: 'medications' } }
    /**
     * Find zero or one Medications that matches the filter.
     * @param {medicationsFindUniqueArgs} args - Arguments to find a Medications
     * @example
     * // Get one Medications
     * const medications = await prisma.medications.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends medicationsFindUniqueArgs>(args: SelectSubset<T, medicationsFindUniqueArgs<ExtArgs>>): Prisma__medicationsClient<$Result.GetResult<Prisma.$medicationsPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Medications that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {medicationsFindUniqueOrThrowArgs} args - Arguments to find a Medications
     * @example
     * // Get one Medications
     * const medications = await prisma.medications.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends medicationsFindUniqueOrThrowArgs>(args: SelectSubset<T, medicationsFindUniqueOrThrowArgs<ExtArgs>>): Prisma__medicationsClient<$Result.GetResult<Prisma.$medicationsPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Medications that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {medicationsFindFirstArgs} args - Arguments to find a Medications
     * @example
     * // Get one Medications
     * const medications = await prisma.medications.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends medicationsFindFirstArgs>(args?: SelectSubset<T, medicationsFindFirstArgs<ExtArgs>>): Prisma__medicationsClient<$Result.GetResult<Prisma.$medicationsPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Medications that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {medicationsFindFirstOrThrowArgs} args - Arguments to find a Medications
     * @example
     * // Get one Medications
     * const medications = await prisma.medications.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends medicationsFindFirstOrThrowArgs>(args?: SelectSubset<T, medicationsFindFirstOrThrowArgs<ExtArgs>>): Prisma__medicationsClient<$Result.GetResult<Prisma.$medicationsPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Medications that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {medicationsFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Medications
     * const medications = await prisma.medications.findMany()
     * 
     * // Get first 10 Medications
     * const medications = await prisma.medications.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const medicationsWithIdOnly = await prisma.medications.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends medicationsFindManyArgs>(args?: SelectSubset<T, medicationsFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$medicationsPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Medications.
     * @param {medicationsCreateArgs} args - Arguments to create a Medications.
     * @example
     * // Create one Medications
     * const Medications = await prisma.medications.create({
     *   data: {
     *     // ... data to create a Medications
     *   }
     * })
     * 
     */
    create<T extends medicationsCreateArgs>(args: SelectSubset<T, medicationsCreateArgs<ExtArgs>>): Prisma__medicationsClient<$Result.GetResult<Prisma.$medicationsPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Medications.
     * @param {medicationsCreateManyArgs} args - Arguments to create many Medications.
     * @example
     * // Create many Medications
     * const medications = await prisma.medications.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends medicationsCreateManyArgs>(args?: SelectSubset<T, medicationsCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Medications and returns the data saved in the database.
     * @param {medicationsCreateManyAndReturnArgs} args - Arguments to create many Medications.
     * @example
     * // Create many Medications
     * const medications = await prisma.medications.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Medications and only return the `id`
     * const medicationsWithIdOnly = await prisma.medications.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends medicationsCreateManyAndReturnArgs>(args?: SelectSubset<T, medicationsCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$medicationsPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Medications.
     * @param {medicationsDeleteArgs} args - Arguments to delete one Medications.
     * @example
     * // Delete one Medications
     * const Medications = await prisma.medications.delete({
     *   where: {
     *     // ... filter to delete one Medications
     *   }
     * })
     * 
     */
    delete<T extends medicationsDeleteArgs>(args: SelectSubset<T, medicationsDeleteArgs<ExtArgs>>): Prisma__medicationsClient<$Result.GetResult<Prisma.$medicationsPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Medications.
     * @param {medicationsUpdateArgs} args - Arguments to update one Medications.
     * @example
     * // Update one Medications
     * const medications = await prisma.medications.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends medicationsUpdateArgs>(args: SelectSubset<T, medicationsUpdateArgs<ExtArgs>>): Prisma__medicationsClient<$Result.GetResult<Prisma.$medicationsPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Medications.
     * @param {medicationsDeleteManyArgs} args - Arguments to filter Medications to delete.
     * @example
     * // Delete a few Medications
     * const { count } = await prisma.medications.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends medicationsDeleteManyArgs>(args?: SelectSubset<T, medicationsDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Medications.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {medicationsUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Medications
     * const medications = await prisma.medications.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends medicationsUpdateManyArgs>(args: SelectSubset<T, medicationsUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Medications and returns the data updated in the database.
     * @param {medicationsUpdateManyAndReturnArgs} args - Arguments to update many Medications.
     * @example
     * // Update many Medications
     * const medications = await prisma.medications.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Medications and only return the `id`
     * const medicationsWithIdOnly = await prisma.medications.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends medicationsUpdateManyAndReturnArgs>(args: SelectSubset<T, medicationsUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$medicationsPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Medications.
     * @param {medicationsUpsertArgs} args - Arguments to update or create a Medications.
     * @example
     * // Update or create a Medications
     * const medications = await prisma.medications.upsert({
     *   create: {
     *     // ... data to create a Medications
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Medications we want to update
     *   }
     * })
     */
    upsert<T extends medicationsUpsertArgs>(args: SelectSubset<T, medicationsUpsertArgs<ExtArgs>>): Prisma__medicationsClient<$Result.GetResult<Prisma.$medicationsPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Medications.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {medicationsCountArgs} args - Arguments to filter Medications to count.
     * @example
     * // Count the number of Medications
     * const count = await prisma.medications.count({
     *   where: {
     *     // ... the filter for the Medications we want to count
     *   }
     * })
    **/
    count<T extends medicationsCountArgs>(
      args?: Subset<T, medicationsCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], MedicationsCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Medications.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {MedicationsAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends MedicationsAggregateArgs>(args: Subset<T, MedicationsAggregateArgs>): Prisma.PrismaPromise<GetMedicationsAggregateType<T>>

    /**
     * Group by Medications.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {medicationsGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends medicationsGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: medicationsGroupByArgs['orderBy'] }
        : { orderBy?: medicationsGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, medicationsGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetMedicationsGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the medications model
   */
  readonly fields: medicationsFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for medications.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__medicationsClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    adherence<T extends medications$adherenceArgs<ExtArgs> = {}>(args?: Subset<T, medications$adherenceArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$adherencePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    user<T extends usersDefaultArgs<ExtArgs> = {}>(args?: Subset<T, usersDefaultArgs<ExtArgs>>): Prisma__usersClient<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    reminders<T extends medications$remindersArgs<ExtArgs> = {}>(args?: Subset<T, medications$remindersArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$remindersPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the medications model
   */
  interface medicationsFieldRefs {
    readonly id: FieldRef<"medications", 'String'>
    readonly user_id: FieldRef<"medications", 'String'>
    readonly name: FieldRef<"medications", 'String'>
    readonly dosage: FieldRef<"medications", 'Json'>
    readonly frequency: FieldRef<"medications", 'Json'>
    readonly scheduled_times: FieldRef<"medications", 'String[]'>
    readonly instructions: FieldRef<"medications", 'String'>
    readonly start_date: FieldRef<"medications", 'DateTime'>
    readonly end_date: FieldRef<"medications", 'DateTime'>
    readonly refill_reminder: FieldRef<"medications", 'Json'>
    readonly side_effects_to_watch: FieldRef<"medications", 'String[]'>
    readonly active: FieldRef<"medications", 'Boolean'>
    readonly medication_type: FieldRef<"medications", 'String'>
    readonly image_url: FieldRef<"medications", 'String'>
    readonly created_at: FieldRef<"medications", 'DateTime'>
    readonly updated_at: FieldRef<"medications", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * medications findUnique
   */
  export type medicationsFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the medications
     */
    select?: medicationsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the medications
     */
    omit?: medicationsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: medicationsInclude<ExtArgs> | null
    /**
     * Filter, which medications to fetch.
     */
    where: medicationsWhereUniqueInput
  }

  /**
   * medications findUniqueOrThrow
   */
  export type medicationsFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the medications
     */
    select?: medicationsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the medications
     */
    omit?: medicationsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: medicationsInclude<ExtArgs> | null
    /**
     * Filter, which medications to fetch.
     */
    where: medicationsWhereUniqueInput
  }

  /**
   * medications findFirst
   */
  export type medicationsFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the medications
     */
    select?: medicationsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the medications
     */
    omit?: medicationsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: medicationsInclude<ExtArgs> | null
    /**
     * Filter, which medications to fetch.
     */
    where?: medicationsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of medications to fetch.
     */
    orderBy?: medicationsOrderByWithRelationInput | medicationsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for medications.
     */
    cursor?: medicationsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` medications from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` medications.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of medications.
     */
    distinct?: MedicationsScalarFieldEnum | MedicationsScalarFieldEnum[]
  }

  /**
   * medications findFirstOrThrow
   */
  export type medicationsFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the medications
     */
    select?: medicationsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the medications
     */
    omit?: medicationsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: medicationsInclude<ExtArgs> | null
    /**
     * Filter, which medications to fetch.
     */
    where?: medicationsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of medications to fetch.
     */
    orderBy?: medicationsOrderByWithRelationInput | medicationsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for medications.
     */
    cursor?: medicationsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` medications from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` medications.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of medications.
     */
    distinct?: MedicationsScalarFieldEnum | MedicationsScalarFieldEnum[]
  }

  /**
   * medications findMany
   */
  export type medicationsFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the medications
     */
    select?: medicationsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the medications
     */
    omit?: medicationsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: medicationsInclude<ExtArgs> | null
    /**
     * Filter, which medications to fetch.
     */
    where?: medicationsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of medications to fetch.
     */
    orderBy?: medicationsOrderByWithRelationInput | medicationsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing medications.
     */
    cursor?: medicationsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` medications from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` medications.
     */
    skip?: number
    distinct?: MedicationsScalarFieldEnum | MedicationsScalarFieldEnum[]
  }

  /**
   * medications create
   */
  export type medicationsCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the medications
     */
    select?: medicationsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the medications
     */
    omit?: medicationsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: medicationsInclude<ExtArgs> | null
    /**
     * The data needed to create a medications.
     */
    data: XOR<medicationsCreateInput, medicationsUncheckedCreateInput>
  }

  /**
   * medications createMany
   */
  export type medicationsCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many medications.
     */
    data: medicationsCreateManyInput | medicationsCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * medications createManyAndReturn
   */
  export type medicationsCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the medications
     */
    select?: medicationsSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the medications
     */
    omit?: medicationsOmit<ExtArgs> | null
    /**
     * The data used to create many medications.
     */
    data: medicationsCreateManyInput | medicationsCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: medicationsIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * medications update
   */
  export type medicationsUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the medications
     */
    select?: medicationsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the medications
     */
    omit?: medicationsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: medicationsInclude<ExtArgs> | null
    /**
     * The data needed to update a medications.
     */
    data: XOR<medicationsUpdateInput, medicationsUncheckedUpdateInput>
    /**
     * Choose, which medications to update.
     */
    where: medicationsWhereUniqueInput
  }

  /**
   * medications updateMany
   */
  export type medicationsUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update medications.
     */
    data: XOR<medicationsUpdateManyMutationInput, medicationsUncheckedUpdateManyInput>
    /**
     * Filter which medications to update
     */
    where?: medicationsWhereInput
    /**
     * Limit how many medications to update.
     */
    limit?: number
  }

  /**
   * medications updateManyAndReturn
   */
  export type medicationsUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the medications
     */
    select?: medicationsSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the medications
     */
    omit?: medicationsOmit<ExtArgs> | null
    /**
     * The data used to update medications.
     */
    data: XOR<medicationsUpdateManyMutationInput, medicationsUncheckedUpdateManyInput>
    /**
     * Filter which medications to update
     */
    where?: medicationsWhereInput
    /**
     * Limit how many medications to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: medicationsIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * medications upsert
   */
  export type medicationsUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the medications
     */
    select?: medicationsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the medications
     */
    omit?: medicationsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: medicationsInclude<ExtArgs> | null
    /**
     * The filter to search for the medications to update in case it exists.
     */
    where: medicationsWhereUniqueInput
    /**
     * In case the medications found by the `where` argument doesn't exist, create a new medications with this data.
     */
    create: XOR<medicationsCreateInput, medicationsUncheckedCreateInput>
    /**
     * In case the medications was found with the provided `where` argument, update it with this data.
     */
    update: XOR<medicationsUpdateInput, medicationsUncheckedUpdateInput>
  }

  /**
   * medications delete
   */
  export type medicationsDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the medications
     */
    select?: medicationsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the medications
     */
    omit?: medicationsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: medicationsInclude<ExtArgs> | null
    /**
     * Filter which medications to delete.
     */
    where: medicationsWhereUniqueInput
  }

  /**
   * medications deleteMany
   */
  export type medicationsDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which medications to delete
     */
    where?: medicationsWhereInput
    /**
     * Limit how many medications to delete.
     */
    limit?: number
  }

  /**
   * medications.adherence
   */
  export type medications$adherenceArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the adherence
     */
    select?: adherenceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the adherence
     */
    omit?: adherenceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: adherenceInclude<ExtArgs> | null
    where?: adherenceWhereInput
    orderBy?: adherenceOrderByWithRelationInput | adherenceOrderByWithRelationInput[]
    cursor?: adherenceWhereUniqueInput
    take?: number
    skip?: number
    distinct?: AdherenceScalarFieldEnum | AdherenceScalarFieldEnum[]
  }

  /**
   * medications.reminders
   */
  export type medications$remindersArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the reminders
     */
    select?: remindersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the reminders
     */
    omit?: remindersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: remindersInclude<ExtArgs> | null
    where?: remindersWhereInput
    orderBy?: remindersOrderByWithRelationInput | remindersOrderByWithRelationInput[]
    cursor?: remindersWhereUniqueInput
    take?: number
    skip?: number
    distinct?: RemindersScalarFieldEnum | RemindersScalarFieldEnum[]
  }

  /**
   * medications without action
   */
  export type medicationsDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the medications
     */
    select?: medicationsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the medications
     */
    omit?: medicationsOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: medicationsInclude<ExtArgs> | null
  }


  /**
   * Model adherence
   */

  export type AggregateAdherence = {
    _count: AdherenceCountAggregateOutputType | null
    _min: AdherenceMinAggregateOutputType | null
    _max: AdherenceMaxAggregateOutputType | null
  }

  export type AdherenceMinAggregateOutputType = {
    id: string | null
    user_id: string | null
    medication_id: string | null
    scheduled_time: string | null
    scheduled_date: Date | null
    taken_time: Date | null
    status: string | null
    notes: string | null
    reminder_sent: boolean | null
    created_at: Date | null
    updated_at: Date | null
  }

  export type AdherenceMaxAggregateOutputType = {
    id: string | null
    user_id: string | null
    medication_id: string | null
    scheduled_time: string | null
    scheduled_date: Date | null
    taken_time: Date | null
    status: string | null
    notes: string | null
    reminder_sent: boolean | null
    created_at: Date | null
    updated_at: Date | null
  }

  export type AdherenceCountAggregateOutputType = {
    id: number
    user_id: number
    medication_id: number
    scheduled_time: number
    scheduled_date: number
    taken_time: number
    status: number
    notes: number
    reminder_sent: number
    side_effects_reported: number
    dosage_taken: number
    created_at: number
    updated_at: number
    _all: number
  }


  export type AdherenceMinAggregateInputType = {
    id?: true
    user_id?: true
    medication_id?: true
    scheduled_time?: true
    scheduled_date?: true
    taken_time?: true
    status?: true
    notes?: true
    reminder_sent?: true
    created_at?: true
    updated_at?: true
  }

  export type AdherenceMaxAggregateInputType = {
    id?: true
    user_id?: true
    medication_id?: true
    scheduled_time?: true
    scheduled_date?: true
    taken_time?: true
    status?: true
    notes?: true
    reminder_sent?: true
    created_at?: true
    updated_at?: true
  }

  export type AdherenceCountAggregateInputType = {
    id?: true
    user_id?: true
    medication_id?: true
    scheduled_time?: true
    scheduled_date?: true
    taken_time?: true
    status?: true
    notes?: true
    reminder_sent?: true
    side_effects_reported?: true
    dosage_taken?: true
    created_at?: true
    updated_at?: true
    _all?: true
  }

  export type AdherenceAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which adherence to aggregate.
     */
    where?: adherenceWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of adherences to fetch.
     */
    orderBy?: adherenceOrderByWithRelationInput | adherenceOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: adherenceWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` adherences from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` adherences.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned adherences
    **/
    _count?: true | AdherenceCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: AdherenceMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: AdherenceMaxAggregateInputType
  }

  export type GetAdherenceAggregateType<T extends AdherenceAggregateArgs> = {
        [P in keyof T & keyof AggregateAdherence]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateAdherence[P]>
      : GetScalarType<T[P], AggregateAdherence[P]>
  }




  export type adherenceGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: adherenceWhereInput
    orderBy?: adherenceOrderByWithAggregationInput | adherenceOrderByWithAggregationInput[]
    by: AdherenceScalarFieldEnum[] | AdherenceScalarFieldEnum
    having?: adherenceScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: AdherenceCountAggregateInputType | true
    _min?: AdherenceMinAggregateInputType
    _max?: AdherenceMaxAggregateInputType
  }

  export type AdherenceGroupByOutputType = {
    id: string
    user_id: string
    medication_id: string
    scheduled_time: string
    scheduled_date: Date
    taken_time: Date | null
    status: string | null
    notes: string | null
    reminder_sent: boolean | null
    side_effects_reported: string[]
    dosage_taken: JsonValue | null
    created_at: Date | null
    updated_at: Date | null
    _count: AdherenceCountAggregateOutputType | null
    _min: AdherenceMinAggregateOutputType | null
    _max: AdherenceMaxAggregateOutputType | null
  }

  type GetAdherenceGroupByPayload<T extends adherenceGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<AdherenceGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof AdherenceGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], AdherenceGroupByOutputType[P]>
            : GetScalarType<T[P], AdherenceGroupByOutputType[P]>
        }
      >
    >


  export type adherenceSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    user_id?: boolean
    medication_id?: boolean
    scheduled_time?: boolean
    scheduled_date?: boolean
    taken_time?: boolean
    status?: boolean
    notes?: boolean
    reminder_sent?: boolean
    side_effects_reported?: boolean
    dosage_taken?: boolean
    created_at?: boolean
    updated_at?: boolean
    medication?: boolean | medicationsDefaultArgs<ExtArgs>
    user?: boolean | usersDefaultArgs<ExtArgs>
    reminders?: boolean | adherence$remindersArgs<ExtArgs>
    _count?: boolean | AdherenceCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["adherence"]>

  export type adherenceSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    user_id?: boolean
    medication_id?: boolean
    scheduled_time?: boolean
    scheduled_date?: boolean
    taken_time?: boolean
    status?: boolean
    notes?: boolean
    reminder_sent?: boolean
    side_effects_reported?: boolean
    dosage_taken?: boolean
    created_at?: boolean
    updated_at?: boolean
    medication?: boolean | medicationsDefaultArgs<ExtArgs>
    user?: boolean | usersDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["adherence"]>

  export type adherenceSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    user_id?: boolean
    medication_id?: boolean
    scheduled_time?: boolean
    scheduled_date?: boolean
    taken_time?: boolean
    status?: boolean
    notes?: boolean
    reminder_sent?: boolean
    side_effects_reported?: boolean
    dosage_taken?: boolean
    created_at?: boolean
    updated_at?: boolean
    medication?: boolean | medicationsDefaultArgs<ExtArgs>
    user?: boolean | usersDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["adherence"]>

  export type adherenceSelectScalar = {
    id?: boolean
    user_id?: boolean
    medication_id?: boolean
    scheduled_time?: boolean
    scheduled_date?: boolean
    taken_time?: boolean
    status?: boolean
    notes?: boolean
    reminder_sent?: boolean
    side_effects_reported?: boolean
    dosage_taken?: boolean
    created_at?: boolean
    updated_at?: boolean
  }

  export type adherenceOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "user_id" | "medication_id" | "scheduled_time" | "scheduled_date" | "taken_time" | "status" | "notes" | "reminder_sent" | "side_effects_reported" | "dosage_taken" | "created_at" | "updated_at", ExtArgs["result"]["adherence"]>
  export type adherenceInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    medication?: boolean | medicationsDefaultArgs<ExtArgs>
    user?: boolean | usersDefaultArgs<ExtArgs>
    reminders?: boolean | adherence$remindersArgs<ExtArgs>
    _count?: boolean | AdherenceCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type adherenceIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    medication?: boolean | medicationsDefaultArgs<ExtArgs>
    user?: boolean | usersDefaultArgs<ExtArgs>
  }
  export type adherenceIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    medication?: boolean | medicationsDefaultArgs<ExtArgs>
    user?: boolean | usersDefaultArgs<ExtArgs>
  }

  export type $adherencePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "adherence"
    objects: {
      medication: Prisma.$medicationsPayload<ExtArgs>
      user: Prisma.$usersPayload<ExtArgs>
      reminders: Prisma.$remindersPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      user_id: string
      medication_id: string
      scheduled_time: string
      scheduled_date: Date
      taken_time: Date | null
      status: string | null
      notes: string | null
      reminder_sent: boolean | null
      side_effects_reported: string[]
      dosage_taken: Prisma.JsonValue | null
      created_at: Date | null
      updated_at: Date | null
    }, ExtArgs["result"]["adherence"]>
    composites: {}
  }

  type adherenceGetPayload<S extends boolean | null | undefined | adherenceDefaultArgs> = $Result.GetResult<Prisma.$adherencePayload, S>

  type adherenceCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<adherenceFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: AdherenceCountAggregateInputType | true
    }

  export interface adherenceDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['adherence'], meta: { name: 'adherence' } }
    /**
     * Find zero or one Adherence that matches the filter.
     * @param {adherenceFindUniqueArgs} args - Arguments to find a Adherence
     * @example
     * // Get one Adherence
     * const adherence = await prisma.adherence.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends adherenceFindUniqueArgs>(args: SelectSubset<T, adherenceFindUniqueArgs<ExtArgs>>): Prisma__adherenceClient<$Result.GetResult<Prisma.$adherencePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Adherence that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {adherenceFindUniqueOrThrowArgs} args - Arguments to find a Adherence
     * @example
     * // Get one Adherence
     * const adherence = await prisma.adherence.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends adherenceFindUniqueOrThrowArgs>(args: SelectSubset<T, adherenceFindUniqueOrThrowArgs<ExtArgs>>): Prisma__adherenceClient<$Result.GetResult<Prisma.$adherencePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Adherence that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {adherenceFindFirstArgs} args - Arguments to find a Adherence
     * @example
     * // Get one Adherence
     * const adherence = await prisma.adherence.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends adherenceFindFirstArgs>(args?: SelectSubset<T, adherenceFindFirstArgs<ExtArgs>>): Prisma__adherenceClient<$Result.GetResult<Prisma.$adherencePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Adherence that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {adherenceFindFirstOrThrowArgs} args - Arguments to find a Adherence
     * @example
     * // Get one Adherence
     * const adherence = await prisma.adherence.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends adherenceFindFirstOrThrowArgs>(args?: SelectSubset<T, adherenceFindFirstOrThrowArgs<ExtArgs>>): Prisma__adherenceClient<$Result.GetResult<Prisma.$adherencePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Adherences that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {adherenceFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Adherences
     * const adherences = await prisma.adherence.findMany()
     * 
     * // Get first 10 Adherences
     * const adherences = await prisma.adherence.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const adherenceWithIdOnly = await prisma.adherence.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends adherenceFindManyArgs>(args?: SelectSubset<T, adherenceFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$adherencePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Adherence.
     * @param {adherenceCreateArgs} args - Arguments to create a Adherence.
     * @example
     * // Create one Adherence
     * const Adherence = await prisma.adherence.create({
     *   data: {
     *     // ... data to create a Adherence
     *   }
     * })
     * 
     */
    create<T extends adherenceCreateArgs>(args: SelectSubset<T, adherenceCreateArgs<ExtArgs>>): Prisma__adherenceClient<$Result.GetResult<Prisma.$adherencePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Adherences.
     * @param {adherenceCreateManyArgs} args - Arguments to create many Adherences.
     * @example
     * // Create many Adherences
     * const adherence = await prisma.adherence.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends adherenceCreateManyArgs>(args?: SelectSubset<T, adherenceCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Adherences and returns the data saved in the database.
     * @param {adherenceCreateManyAndReturnArgs} args - Arguments to create many Adherences.
     * @example
     * // Create many Adherences
     * const adherence = await prisma.adherence.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Adherences and only return the `id`
     * const adherenceWithIdOnly = await prisma.adherence.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends adherenceCreateManyAndReturnArgs>(args?: SelectSubset<T, adherenceCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$adherencePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Adherence.
     * @param {adherenceDeleteArgs} args - Arguments to delete one Adherence.
     * @example
     * // Delete one Adherence
     * const Adherence = await prisma.adherence.delete({
     *   where: {
     *     // ... filter to delete one Adherence
     *   }
     * })
     * 
     */
    delete<T extends adherenceDeleteArgs>(args: SelectSubset<T, adherenceDeleteArgs<ExtArgs>>): Prisma__adherenceClient<$Result.GetResult<Prisma.$adherencePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Adherence.
     * @param {adherenceUpdateArgs} args - Arguments to update one Adherence.
     * @example
     * // Update one Adherence
     * const adherence = await prisma.adherence.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends adherenceUpdateArgs>(args: SelectSubset<T, adherenceUpdateArgs<ExtArgs>>): Prisma__adherenceClient<$Result.GetResult<Prisma.$adherencePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Adherences.
     * @param {adherenceDeleteManyArgs} args - Arguments to filter Adherences to delete.
     * @example
     * // Delete a few Adherences
     * const { count } = await prisma.adherence.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends adherenceDeleteManyArgs>(args?: SelectSubset<T, adherenceDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Adherences.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {adherenceUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Adherences
     * const adherence = await prisma.adherence.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends adherenceUpdateManyArgs>(args: SelectSubset<T, adherenceUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Adherences and returns the data updated in the database.
     * @param {adherenceUpdateManyAndReturnArgs} args - Arguments to update many Adherences.
     * @example
     * // Update many Adherences
     * const adherence = await prisma.adherence.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Adherences and only return the `id`
     * const adherenceWithIdOnly = await prisma.adherence.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends adherenceUpdateManyAndReturnArgs>(args: SelectSubset<T, adherenceUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$adherencePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Adherence.
     * @param {adherenceUpsertArgs} args - Arguments to update or create a Adherence.
     * @example
     * // Update or create a Adherence
     * const adherence = await prisma.adherence.upsert({
     *   create: {
     *     // ... data to create a Adherence
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Adherence we want to update
     *   }
     * })
     */
    upsert<T extends adherenceUpsertArgs>(args: SelectSubset<T, adherenceUpsertArgs<ExtArgs>>): Prisma__adherenceClient<$Result.GetResult<Prisma.$adherencePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Adherences.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {adherenceCountArgs} args - Arguments to filter Adherences to count.
     * @example
     * // Count the number of Adherences
     * const count = await prisma.adherence.count({
     *   where: {
     *     // ... the filter for the Adherences we want to count
     *   }
     * })
    **/
    count<T extends adherenceCountArgs>(
      args?: Subset<T, adherenceCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], AdherenceCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Adherence.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AdherenceAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends AdherenceAggregateArgs>(args: Subset<T, AdherenceAggregateArgs>): Prisma.PrismaPromise<GetAdherenceAggregateType<T>>

    /**
     * Group by Adherence.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {adherenceGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends adherenceGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: adherenceGroupByArgs['orderBy'] }
        : { orderBy?: adherenceGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, adherenceGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetAdherenceGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the adherence model
   */
  readonly fields: adherenceFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for adherence.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__adherenceClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    medication<T extends medicationsDefaultArgs<ExtArgs> = {}>(args?: Subset<T, medicationsDefaultArgs<ExtArgs>>): Prisma__medicationsClient<$Result.GetResult<Prisma.$medicationsPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    user<T extends usersDefaultArgs<ExtArgs> = {}>(args?: Subset<T, usersDefaultArgs<ExtArgs>>): Prisma__usersClient<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    reminders<T extends adherence$remindersArgs<ExtArgs> = {}>(args?: Subset<T, adherence$remindersArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$remindersPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the adherence model
   */
  interface adherenceFieldRefs {
    readonly id: FieldRef<"adherence", 'String'>
    readonly user_id: FieldRef<"adherence", 'String'>
    readonly medication_id: FieldRef<"adherence", 'String'>
    readonly scheduled_time: FieldRef<"adherence", 'String'>
    readonly scheduled_date: FieldRef<"adherence", 'DateTime'>
    readonly taken_time: FieldRef<"adherence", 'DateTime'>
    readonly status: FieldRef<"adherence", 'String'>
    readonly notes: FieldRef<"adherence", 'String'>
    readonly reminder_sent: FieldRef<"adherence", 'Boolean'>
    readonly side_effects_reported: FieldRef<"adherence", 'String[]'>
    readonly dosage_taken: FieldRef<"adherence", 'Json'>
    readonly created_at: FieldRef<"adherence", 'DateTime'>
    readonly updated_at: FieldRef<"adherence", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * adherence findUnique
   */
  export type adherenceFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the adherence
     */
    select?: adherenceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the adherence
     */
    omit?: adherenceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: adherenceInclude<ExtArgs> | null
    /**
     * Filter, which adherence to fetch.
     */
    where: adherenceWhereUniqueInput
  }

  /**
   * adherence findUniqueOrThrow
   */
  export type adherenceFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the adherence
     */
    select?: adherenceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the adherence
     */
    omit?: adherenceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: adherenceInclude<ExtArgs> | null
    /**
     * Filter, which adherence to fetch.
     */
    where: adherenceWhereUniqueInput
  }

  /**
   * adherence findFirst
   */
  export type adherenceFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the adherence
     */
    select?: adherenceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the adherence
     */
    omit?: adherenceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: adherenceInclude<ExtArgs> | null
    /**
     * Filter, which adherence to fetch.
     */
    where?: adherenceWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of adherences to fetch.
     */
    orderBy?: adherenceOrderByWithRelationInput | adherenceOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for adherences.
     */
    cursor?: adherenceWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` adherences from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` adherences.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of adherences.
     */
    distinct?: AdherenceScalarFieldEnum | AdherenceScalarFieldEnum[]
  }

  /**
   * adherence findFirstOrThrow
   */
  export type adherenceFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the adherence
     */
    select?: adherenceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the adherence
     */
    omit?: adherenceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: adherenceInclude<ExtArgs> | null
    /**
     * Filter, which adherence to fetch.
     */
    where?: adherenceWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of adherences to fetch.
     */
    orderBy?: adherenceOrderByWithRelationInput | adherenceOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for adherences.
     */
    cursor?: adherenceWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` adherences from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` adherences.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of adherences.
     */
    distinct?: AdherenceScalarFieldEnum | AdherenceScalarFieldEnum[]
  }

  /**
   * adherence findMany
   */
  export type adherenceFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the adherence
     */
    select?: adherenceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the adherence
     */
    omit?: adherenceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: adherenceInclude<ExtArgs> | null
    /**
     * Filter, which adherences to fetch.
     */
    where?: adherenceWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of adherences to fetch.
     */
    orderBy?: adherenceOrderByWithRelationInput | adherenceOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing adherences.
     */
    cursor?: adherenceWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` adherences from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` adherences.
     */
    skip?: number
    distinct?: AdherenceScalarFieldEnum | AdherenceScalarFieldEnum[]
  }

  /**
   * adherence create
   */
  export type adherenceCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the adherence
     */
    select?: adherenceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the adherence
     */
    omit?: adherenceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: adherenceInclude<ExtArgs> | null
    /**
     * The data needed to create a adherence.
     */
    data: XOR<adherenceCreateInput, adherenceUncheckedCreateInput>
  }

  /**
   * adherence createMany
   */
  export type adherenceCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many adherences.
     */
    data: adherenceCreateManyInput | adherenceCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * adherence createManyAndReturn
   */
  export type adherenceCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the adherence
     */
    select?: adherenceSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the adherence
     */
    omit?: adherenceOmit<ExtArgs> | null
    /**
     * The data used to create many adherences.
     */
    data: adherenceCreateManyInput | adherenceCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: adherenceIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * adherence update
   */
  export type adherenceUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the adherence
     */
    select?: adherenceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the adherence
     */
    omit?: adherenceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: adherenceInclude<ExtArgs> | null
    /**
     * The data needed to update a adherence.
     */
    data: XOR<adherenceUpdateInput, adherenceUncheckedUpdateInput>
    /**
     * Choose, which adherence to update.
     */
    where: adherenceWhereUniqueInput
  }

  /**
   * adherence updateMany
   */
  export type adherenceUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update adherences.
     */
    data: XOR<adherenceUpdateManyMutationInput, adherenceUncheckedUpdateManyInput>
    /**
     * Filter which adherences to update
     */
    where?: adherenceWhereInput
    /**
     * Limit how many adherences to update.
     */
    limit?: number
  }

  /**
   * adherence updateManyAndReturn
   */
  export type adherenceUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the adherence
     */
    select?: adherenceSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the adherence
     */
    omit?: adherenceOmit<ExtArgs> | null
    /**
     * The data used to update adherences.
     */
    data: XOR<adherenceUpdateManyMutationInput, adherenceUncheckedUpdateManyInput>
    /**
     * Filter which adherences to update
     */
    where?: adherenceWhereInput
    /**
     * Limit how many adherences to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: adherenceIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * adherence upsert
   */
  export type adherenceUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the adherence
     */
    select?: adherenceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the adherence
     */
    omit?: adherenceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: adherenceInclude<ExtArgs> | null
    /**
     * The filter to search for the adherence to update in case it exists.
     */
    where: adherenceWhereUniqueInput
    /**
     * In case the adherence found by the `where` argument doesn't exist, create a new adherence with this data.
     */
    create: XOR<adherenceCreateInput, adherenceUncheckedCreateInput>
    /**
     * In case the adherence was found with the provided `where` argument, update it with this data.
     */
    update: XOR<adherenceUpdateInput, adherenceUncheckedUpdateInput>
  }

  /**
   * adherence delete
   */
  export type adherenceDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the adherence
     */
    select?: adherenceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the adherence
     */
    omit?: adherenceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: adherenceInclude<ExtArgs> | null
    /**
     * Filter which adherence to delete.
     */
    where: adherenceWhereUniqueInput
  }

  /**
   * adherence deleteMany
   */
  export type adherenceDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which adherences to delete
     */
    where?: adherenceWhereInput
    /**
     * Limit how many adherences to delete.
     */
    limit?: number
  }

  /**
   * adherence.reminders
   */
  export type adherence$remindersArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the reminders
     */
    select?: remindersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the reminders
     */
    omit?: remindersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: remindersInclude<ExtArgs> | null
    where?: remindersWhereInput
    orderBy?: remindersOrderByWithRelationInput | remindersOrderByWithRelationInput[]
    cursor?: remindersWhereUniqueInput
    take?: number
    skip?: number
    distinct?: RemindersScalarFieldEnum | RemindersScalarFieldEnum[]
  }

  /**
   * adherence without action
   */
  export type adherenceDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the adherence
     */
    select?: adherenceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the adherence
     */
    omit?: adherenceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: adherenceInclude<ExtArgs> | null
  }


  /**
   * Model reminders
   */

  export type AggregateReminders = {
    _count: RemindersCountAggregateOutputType | null
    _avg: RemindersAvgAggregateOutputType | null
    _sum: RemindersSumAggregateOutputType | null
    _min: RemindersMinAggregateOutputType | null
    _max: RemindersMaxAggregateOutputType | null
  }

  export type RemindersAvgAggregateOutputType = {
    retry_count: number | null
  }

  export type RemindersSumAggregateOutputType = {
    retry_count: number | null
  }

  export type RemindersMinAggregateOutputType = {
    id: string | null
    user_id: string | null
    medication_id: string | null
    scheduled_time: string | null
    scheduled_date: Date | null
    status: string | null
    message: string | null
    retry_count: number | null
    last_retry: Date | null
    adherence_id: string | null
    created_at: Date | null
    updated_at: Date | null
  }

  export type RemindersMaxAggregateOutputType = {
    id: string | null
    user_id: string | null
    medication_id: string | null
    scheduled_time: string | null
    scheduled_date: Date | null
    status: string | null
    message: string | null
    retry_count: number | null
    last_retry: Date | null
    adherence_id: string | null
    created_at: Date | null
    updated_at: Date | null
  }

  export type RemindersCountAggregateOutputType = {
    id: number
    user_id: number
    medication_id: number
    scheduled_time: number
    scheduled_date: number
    status: number
    channels: number
    message: number
    retry_count: number
    last_retry: number
    adherence_id: number
    created_at: number
    updated_at: number
    _all: number
  }


  export type RemindersAvgAggregateInputType = {
    retry_count?: true
  }

  export type RemindersSumAggregateInputType = {
    retry_count?: true
  }

  export type RemindersMinAggregateInputType = {
    id?: true
    user_id?: true
    medication_id?: true
    scheduled_time?: true
    scheduled_date?: true
    status?: true
    message?: true
    retry_count?: true
    last_retry?: true
    adherence_id?: true
    created_at?: true
    updated_at?: true
  }

  export type RemindersMaxAggregateInputType = {
    id?: true
    user_id?: true
    medication_id?: true
    scheduled_time?: true
    scheduled_date?: true
    status?: true
    message?: true
    retry_count?: true
    last_retry?: true
    adherence_id?: true
    created_at?: true
    updated_at?: true
  }

  export type RemindersCountAggregateInputType = {
    id?: true
    user_id?: true
    medication_id?: true
    scheduled_time?: true
    scheduled_date?: true
    status?: true
    channels?: true
    message?: true
    retry_count?: true
    last_retry?: true
    adherence_id?: true
    created_at?: true
    updated_at?: true
    _all?: true
  }

  export type RemindersAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which reminders to aggregate.
     */
    where?: remindersWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of reminders to fetch.
     */
    orderBy?: remindersOrderByWithRelationInput | remindersOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: remindersWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` reminders from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` reminders.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned reminders
    **/
    _count?: true | RemindersCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: RemindersAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: RemindersSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: RemindersMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: RemindersMaxAggregateInputType
  }

  export type GetRemindersAggregateType<T extends RemindersAggregateArgs> = {
        [P in keyof T & keyof AggregateReminders]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateReminders[P]>
      : GetScalarType<T[P], AggregateReminders[P]>
  }




  export type remindersGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: remindersWhereInput
    orderBy?: remindersOrderByWithAggregationInput | remindersOrderByWithAggregationInput[]
    by: RemindersScalarFieldEnum[] | RemindersScalarFieldEnum
    having?: remindersScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: RemindersCountAggregateInputType | true
    _avg?: RemindersAvgAggregateInputType
    _sum?: RemindersSumAggregateInputType
    _min?: RemindersMinAggregateInputType
    _max?: RemindersMaxAggregateInputType
  }

  export type RemindersGroupByOutputType = {
    id: string
    user_id: string
    medication_id: string
    scheduled_time: string
    scheduled_date: Date
    status: string | null
    channels: JsonValue | null
    message: string | null
    retry_count: number | null
    last_retry: Date | null
    adherence_id: string | null
    created_at: Date | null
    updated_at: Date | null
    _count: RemindersCountAggregateOutputType | null
    _avg: RemindersAvgAggregateOutputType | null
    _sum: RemindersSumAggregateOutputType | null
    _min: RemindersMinAggregateOutputType | null
    _max: RemindersMaxAggregateOutputType | null
  }

  type GetRemindersGroupByPayload<T extends remindersGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<RemindersGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof RemindersGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], RemindersGroupByOutputType[P]>
            : GetScalarType<T[P], RemindersGroupByOutputType[P]>
        }
      >
    >


  export type remindersSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    user_id?: boolean
    medication_id?: boolean
    scheduled_time?: boolean
    scheduled_date?: boolean
    status?: boolean
    channels?: boolean
    message?: boolean
    retry_count?: boolean
    last_retry?: boolean
    adherence_id?: boolean
    created_at?: boolean
    updated_at?: boolean
    adherence?: boolean | reminders$adherenceArgs<ExtArgs>
    medication?: boolean | medicationsDefaultArgs<ExtArgs>
    user?: boolean | usersDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["reminders"]>

  export type remindersSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    user_id?: boolean
    medication_id?: boolean
    scheduled_time?: boolean
    scheduled_date?: boolean
    status?: boolean
    channels?: boolean
    message?: boolean
    retry_count?: boolean
    last_retry?: boolean
    adherence_id?: boolean
    created_at?: boolean
    updated_at?: boolean
    adherence?: boolean | reminders$adherenceArgs<ExtArgs>
    medication?: boolean | medicationsDefaultArgs<ExtArgs>
    user?: boolean | usersDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["reminders"]>

  export type remindersSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    user_id?: boolean
    medication_id?: boolean
    scheduled_time?: boolean
    scheduled_date?: boolean
    status?: boolean
    channels?: boolean
    message?: boolean
    retry_count?: boolean
    last_retry?: boolean
    adherence_id?: boolean
    created_at?: boolean
    updated_at?: boolean
    adherence?: boolean | reminders$adherenceArgs<ExtArgs>
    medication?: boolean | medicationsDefaultArgs<ExtArgs>
    user?: boolean | usersDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["reminders"]>

  export type remindersSelectScalar = {
    id?: boolean
    user_id?: boolean
    medication_id?: boolean
    scheduled_time?: boolean
    scheduled_date?: boolean
    status?: boolean
    channels?: boolean
    message?: boolean
    retry_count?: boolean
    last_retry?: boolean
    adherence_id?: boolean
    created_at?: boolean
    updated_at?: boolean
  }

  export type remindersOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "user_id" | "medication_id" | "scheduled_time" | "scheduled_date" | "status" | "channels" | "message" | "retry_count" | "last_retry" | "adherence_id" | "created_at" | "updated_at", ExtArgs["result"]["reminders"]>
  export type remindersInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    adherence?: boolean | reminders$adherenceArgs<ExtArgs>
    medication?: boolean | medicationsDefaultArgs<ExtArgs>
    user?: boolean | usersDefaultArgs<ExtArgs>
  }
  export type remindersIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    adherence?: boolean | reminders$adherenceArgs<ExtArgs>
    medication?: boolean | medicationsDefaultArgs<ExtArgs>
    user?: boolean | usersDefaultArgs<ExtArgs>
  }
  export type remindersIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    adherence?: boolean | reminders$adherenceArgs<ExtArgs>
    medication?: boolean | medicationsDefaultArgs<ExtArgs>
    user?: boolean | usersDefaultArgs<ExtArgs>
  }

  export type $remindersPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "reminders"
    objects: {
      adherence: Prisma.$adherencePayload<ExtArgs> | null
      medication: Prisma.$medicationsPayload<ExtArgs>
      user: Prisma.$usersPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      user_id: string
      medication_id: string
      scheduled_time: string
      scheduled_date: Date
      status: string | null
      channels: Prisma.JsonValue | null
      message: string | null
      retry_count: number | null
      last_retry: Date | null
      adherence_id: string | null
      created_at: Date | null
      updated_at: Date | null
    }, ExtArgs["result"]["reminders"]>
    composites: {}
  }

  type remindersGetPayload<S extends boolean | null | undefined | remindersDefaultArgs> = $Result.GetResult<Prisma.$remindersPayload, S>

  type remindersCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<remindersFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: RemindersCountAggregateInputType | true
    }

  export interface remindersDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['reminders'], meta: { name: 'reminders' } }
    /**
     * Find zero or one Reminders that matches the filter.
     * @param {remindersFindUniqueArgs} args - Arguments to find a Reminders
     * @example
     * // Get one Reminders
     * const reminders = await prisma.reminders.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends remindersFindUniqueArgs>(args: SelectSubset<T, remindersFindUniqueArgs<ExtArgs>>): Prisma__remindersClient<$Result.GetResult<Prisma.$remindersPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Reminders that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {remindersFindUniqueOrThrowArgs} args - Arguments to find a Reminders
     * @example
     * // Get one Reminders
     * const reminders = await prisma.reminders.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends remindersFindUniqueOrThrowArgs>(args: SelectSubset<T, remindersFindUniqueOrThrowArgs<ExtArgs>>): Prisma__remindersClient<$Result.GetResult<Prisma.$remindersPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Reminders that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {remindersFindFirstArgs} args - Arguments to find a Reminders
     * @example
     * // Get one Reminders
     * const reminders = await prisma.reminders.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends remindersFindFirstArgs>(args?: SelectSubset<T, remindersFindFirstArgs<ExtArgs>>): Prisma__remindersClient<$Result.GetResult<Prisma.$remindersPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Reminders that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {remindersFindFirstOrThrowArgs} args - Arguments to find a Reminders
     * @example
     * // Get one Reminders
     * const reminders = await prisma.reminders.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends remindersFindFirstOrThrowArgs>(args?: SelectSubset<T, remindersFindFirstOrThrowArgs<ExtArgs>>): Prisma__remindersClient<$Result.GetResult<Prisma.$remindersPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Reminders that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {remindersFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Reminders
     * const reminders = await prisma.reminders.findMany()
     * 
     * // Get first 10 Reminders
     * const reminders = await prisma.reminders.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const remindersWithIdOnly = await prisma.reminders.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends remindersFindManyArgs>(args?: SelectSubset<T, remindersFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$remindersPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Reminders.
     * @param {remindersCreateArgs} args - Arguments to create a Reminders.
     * @example
     * // Create one Reminders
     * const Reminders = await prisma.reminders.create({
     *   data: {
     *     // ... data to create a Reminders
     *   }
     * })
     * 
     */
    create<T extends remindersCreateArgs>(args: SelectSubset<T, remindersCreateArgs<ExtArgs>>): Prisma__remindersClient<$Result.GetResult<Prisma.$remindersPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Reminders.
     * @param {remindersCreateManyArgs} args - Arguments to create many Reminders.
     * @example
     * // Create many Reminders
     * const reminders = await prisma.reminders.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends remindersCreateManyArgs>(args?: SelectSubset<T, remindersCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Reminders and returns the data saved in the database.
     * @param {remindersCreateManyAndReturnArgs} args - Arguments to create many Reminders.
     * @example
     * // Create many Reminders
     * const reminders = await prisma.reminders.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Reminders and only return the `id`
     * const remindersWithIdOnly = await prisma.reminders.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends remindersCreateManyAndReturnArgs>(args?: SelectSubset<T, remindersCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$remindersPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Reminders.
     * @param {remindersDeleteArgs} args - Arguments to delete one Reminders.
     * @example
     * // Delete one Reminders
     * const Reminders = await prisma.reminders.delete({
     *   where: {
     *     // ... filter to delete one Reminders
     *   }
     * })
     * 
     */
    delete<T extends remindersDeleteArgs>(args: SelectSubset<T, remindersDeleteArgs<ExtArgs>>): Prisma__remindersClient<$Result.GetResult<Prisma.$remindersPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Reminders.
     * @param {remindersUpdateArgs} args - Arguments to update one Reminders.
     * @example
     * // Update one Reminders
     * const reminders = await prisma.reminders.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends remindersUpdateArgs>(args: SelectSubset<T, remindersUpdateArgs<ExtArgs>>): Prisma__remindersClient<$Result.GetResult<Prisma.$remindersPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Reminders.
     * @param {remindersDeleteManyArgs} args - Arguments to filter Reminders to delete.
     * @example
     * // Delete a few Reminders
     * const { count } = await prisma.reminders.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends remindersDeleteManyArgs>(args?: SelectSubset<T, remindersDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Reminders.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {remindersUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Reminders
     * const reminders = await prisma.reminders.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends remindersUpdateManyArgs>(args: SelectSubset<T, remindersUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Reminders and returns the data updated in the database.
     * @param {remindersUpdateManyAndReturnArgs} args - Arguments to update many Reminders.
     * @example
     * // Update many Reminders
     * const reminders = await prisma.reminders.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Reminders and only return the `id`
     * const remindersWithIdOnly = await prisma.reminders.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends remindersUpdateManyAndReturnArgs>(args: SelectSubset<T, remindersUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$remindersPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Reminders.
     * @param {remindersUpsertArgs} args - Arguments to update or create a Reminders.
     * @example
     * // Update or create a Reminders
     * const reminders = await prisma.reminders.upsert({
     *   create: {
     *     // ... data to create a Reminders
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Reminders we want to update
     *   }
     * })
     */
    upsert<T extends remindersUpsertArgs>(args: SelectSubset<T, remindersUpsertArgs<ExtArgs>>): Prisma__remindersClient<$Result.GetResult<Prisma.$remindersPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Reminders.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {remindersCountArgs} args - Arguments to filter Reminders to count.
     * @example
     * // Count the number of Reminders
     * const count = await prisma.reminders.count({
     *   where: {
     *     // ... the filter for the Reminders we want to count
     *   }
     * })
    **/
    count<T extends remindersCountArgs>(
      args?: Subset<T, remindersCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], RemindersCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Reminders.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RemindersAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends RemindersAggregateArgs>(args: Subset<T, RemindersAggregateArgs>): Prisma.PrismaPromise<GetRemindersAggregateType<T>>

    /**
     * Group by Reminders.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {remindersGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends remindersGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: remindersGroupByArgs['orderBy'] }
        : { orderBy?: remindersGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, remindersGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetRemindersGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the reminders model
   */
  readonly fields: remindersFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for reminders.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__remindersClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    adherence<T extends reminders$adherenceArgs<ExtArgs> = {}>(args?: Subset<T, reminders$adherenceArgs<ExtArgs>>): Prisma__adherenceClient<$Result.GetResult<Prisma.$adherencePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
    medication<T extends medicationsDefaultArgs<ExtArgs> = {}>(args?: Subset<T, medicationsDefaultArgs<ExtArgs>>): Prisma__medicationsClient<$Result.GetResult<Prisma.$medicationsPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    user<T extends usersDefaultArgs<ExtArgs> = {}>(args?: Subset<T, usersDefaultArgs<ExtArgs>>): Prisma__usersClient<$Result.GetResult<Prisma.$usersPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the reminders model
   */
  interface remindersFieldRefs {
    readonly id: FieldRef<"reminders", 'String'>
    readonly user_id: FieldRef<"reminders", 'String'>
    readonly medication_id: FieldRef<"reminders", 'String'>
    readonly scheduled_time: FieldRef<"reminders", 'String'>
    readonly scheduled_date: FieldRef<"reminders", 'DateTime'>
    readonly status: FieldRef<"reminders", 'String'>
    readonly channels: FieldRef<"reminders", 'Json'>
    readonly message: FieldRef<"reminders", 'String'>
    readonly retry_count: FieldRef<"reminders", 'Int'>
    readonly last_retry: FieldRef<"reminders", 'DateTime'>
    readonly adherence_id: FieldRef<"reminders", 'String'>
    readonly created_at: FieldRef<"reminders", 'DateTime'>
    readonly updated_at: FieldRef<"reminders", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * reminders findUnique
   */
  export type remindersFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the reminders
     */
    select?: remindersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the reminders
     */
    omit?: remindersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: remindersInclude<ExtArgs> | null
    /**
     * Filter, which reminders to fetch.
     */
    where: remindersWhereUniqueInput
  }

  /**
   * reminders findUniqueOrThrow
   */
  export type remindersFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the reminders
     */
    select?: remindersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the reminders
     */
    omit?: remindersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: remindersInclude<ExtArgs> | null
    /**
     * Filter, which reminders to fetch.
     */
    where: remindersWhereUniqueInput
  }

  /**
   * reminders findFirst
   */
  export type remindersFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the reminders
     */
    select?: remindersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the reminders
     */
    omit?: remindersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: remindersInclude<ExtArgs> | null
    /**
     * Filter, which reminders to fetch.
     */
    where?: remindersWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of reminders to fetch.
     */
    orderBy?: remindersOrderByWithRelationInput | remindersOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for reminders.
     */
    cursor?: remindersWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` reminders from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` reminders.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of reminders.
     */
    distinct?: RemindersScalarFieldEnum | RemindersScalarFieldEnum[]
  }

  /**
   * reminders findFirstOrThrow
   */
  export type remindersFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the reminders
     */
    select?: remindersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the reminders
     */
    omit?: remindersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: remindersInclude<ExtArgs> | null
    /**
     * Filter, which reminders to fetch.
     */
    where?: remindersWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of reminders to fetch.
     */
    orderBy?: remindersOrderByWithRelationInput | remindersOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for reminders.
     */
    cursor?: remindersWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` reminders from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` reminders.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of reminders.
     */
    distinct?: RemindersScalarFieldEnum | RemindersScalarFieldEnum[]
  }

  /**
   * reminders findMany
   */
  export type remindersFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the reminders
     */
    select?: remindersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the reminders
     */
    omit?: remindersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: remindersInclude<ExtArgs> | null
    /**
     * Filter, which reminders to fetch.
     */
    where?: remindersWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of reminders to fetch.
     */
    orderBy?: remindersOrderByWithRelationInput | remindersOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing reminders.
     */
    cursor?: remindersWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` reminders from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` reminders.
     */
    skip?: number
    distinct?: RemindersScalarFieldEnum | RemindersScalarFieldEnum[]
  }

  /**
   * reminders create
   */
  export type remindersCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the reminders
     */
    select?: remindersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the reminders
     */
    omit?: remindersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: remindersInclude<ExtArgs> | null
    /**
     * The data needed to create a reminders.
     */
    data: XOR<remindersCreateInput, remindersUncheckedCreateInput>
  }

  /**
   * reminders createMany
   */
  export type remindersCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many reminders.
     */
    data: remindersCreateManyInput | remindersCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * reminders createManyAndReturn
   */
  export type remindersCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the reminders
     */
    select?: remindersSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the reminders
     */
    omit?: remindersOmit<ExtArgs> | null
    /**
     * The data used to create many reminders.
     */
    data: remindersCreateManyInput | remindersCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: remindersIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * reminders update
   */
  export type remindersUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the reminders
     */
    select?: remindersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the reminders
     */
    omit?: remindersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: remindersInclude<ExtArgs> | null
    /**
     * The data needed to update a reminders.
     */
    data: XOR<remindersUpdateInput, remindersUncheckedUpdateInput>
    /**
     * Choose, which reminders to update.
     */
    where: remindersWhereUniqueInput
  }

  /**
   * reminders updateMany
   */
  export type remindersUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update reminders.
     */
    data: XOR<remindersUpdateManyMutationInput, remindersUncheckedUpdateManyInput>
    /**
     * Filter which reminders to update
     */
    where?: remindersWhereInput
    /**
     * Limit how many reminders to update.
     */
    limit?: number
  }

  /**
   * reminders updateManyAndReturn
   */
  export type remindersUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the reminders
     */
    select?: remindersSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the reminders
     */
    omit?: remindersOmit<ExtArgs> | null
    /**
     * The data used to update reminders.
     */
    data: XOR<remindersUpdateManyMutationInput, remindersUncheckedUpdateManyInput>
    /**
     * Filter which reminders to update
     */
    where?: remindersWhereInput
    /**
     * Limit how many reminders to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: remindersIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * reminders upsert
   */
  export type remindersUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the reminders
     */
    select?: remindersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the reminders
     */
    omit?: remindersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: remindersInclude<ExtArgs> | null
    /**
     * The filter to search for the reminders to update in case it exists.
     */
    where: remindersWhereUniqueInput
    /**
     * In case the reminders found by the `where` argument doesn't exist, create a new reminders with this data.
     */
    create: XOR<remindersCreateInput, remindersUncheckedCreateInput>
    /**
     * In case the reminders was found with the provided `where` argument, update it with this data.
     */
    update: XOR<remindersUpdateInput, remindersUncheckedUpdateInput>
  }

  /**
   * reminders delete
   */
  export type remindersDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the reminders
     */
    select?: remindersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the reminders
     */
    omit?: remindersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: remindersInclude<ExtArgs> | null
    /**
     * Filter which reminders to delete.
     */
    where: remindersWhereUniqueInput
  }

  /**
   * reminders deleteMany
   */
  export type remindersDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which reminders to delete
     */
    where?: remindersWhereInput
    /**
     * Limit how many reminders to delete.
     */
    limit?: number
  }

  /**
   * reminders.adherence
   */
  export type reminders$adherenceArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the adherence
     */
    select?: adherenceSelect<ExtArgs> | null
    /**
     * Omit specific fields from the adherence
     */
    omit?: adherenceOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: adherenceInclude<ExtArgs> | null
    where?: adherenceWhereInput
  }

  /**
   * reminders without action
   */
  export type remindersDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the reminders
     */
    select?: remindersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the reminders
     */
    omit?: remindersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: remindersInclude<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    ReadUncommitted: 'ReadUncommitted',
    ReadCommitted: 'ReadCommitted',
    RepeatableRead: 'RepeatableRead',
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const UsersScalarFieldEnum: {
    id: 'id',
    email: 'email',
    name: 'name',
    password: 'password',
    date_of_birth: 'date_of_birth',
    gender: 'gender',
    allergies: 'allergies',
    conditions: 'conditions',
    is_admin: 'is_admin',
    phone_number: 'phone_number',
    emergency_contact: 'emergency_contact',
    created_at: 'created_at',
    updated_at: 'updated_at',
    subscription_status: 'subscription_status',
    subscription_plan: 'subscription_plan',
    subscription_expires_at: 'subscription_expires_at',
    subscription_features: 'subscription_features'
  };

  export type UsersScalarFieldEnum = (typeof UsersScalarFieldEnum)[keyof typeof UsersScalarFieldEnum]


  export const User_settingsScalarFieldEnum: {
    id: 'id',
    user_id: 'user_id',
    email_enabled: 'email_enabled',
    preferred_times: 'preferred_times',
    timezone: 'timezone',
    notification_preferences: 'notification_preferences',
    created_at: 'created_at',
    updated_at: 'updated_at'
  };

  export type User_settingsScalarFieldEnum = (typeof User_settingsScalarFieldEnum)[keyof typeof User_settingsScalarFieldEnum]


  export const MedicationsScalarFieldEnum: {
    id: 'id',
    user_id: 'user_id',
    name: 'name',
    dosage: 'dosage',
    frequency: 'frequency',
    scheduled_times: 'scheduled_times',
    instructions: 'instructions',
    start_date: 'start_date',
    end_date: 'end_date',
    refill_reminder: 'refill_reminder',
    side_effects_to_watch: 'side_effects_to_watch',
    active: 'active',
    medication_type: 'medication_type',
    image_url: 'image_url',
    created_at: 'created_at',
    updated_at: 'updated_at'
  };

  export type MedicationsScalarFieldEnum = (typeof MedicationsScalarFieldEnum)[keyof typeof MedicationsScalarFieldEnum]


  export const AdherenceScalarFieldEnum: {
    id: 'id',
    user_id: 'user_id',
    medication_id: 'medication_id',
    scheduled_time: 'scheduled_time',
    scheduled_date: 'scheduled_date',
    taken_time: 'taken_time',
    status: 'status',
    notes: 'notes',
    reminder_sent: 'reminder_sent',
    side_effects_reported: 'side_effects_reported',
    dosage_taken: 'dosage_taken',
    created_at: 'created_at',
    updated_at: 'updated_at'
  };

  export type AdherenceScalarFieldEnum = (typeof AdherenceScalarFieldEnum)[keyof typeof AdherenceScalarFieldEnum]


  export const RemindersScalarFieldEnum: {
    id: 'id',
    user_id: 'user_id',
    medication_id: 'medication_id',
    scheduled_time: 'scheduled_time',
    scheduled_date: 'scheduled_date',
    status: 'status',
    channels: 'channels',
    message: 'message',
    retry_count: 'retry_count',
    last_retry: 'last_retry',
    adherence_id: 'adherence_id',
    created_at: 'created_at',
    updated_at: 'updated_at'
  };

  export type RemindersScalarFieldEnum = (typeof RemindersScalarFieldEnum)[keyof typeof RemindersScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const NullableJsonNullValueInput: {
    DbNull: typeof DbNull,
    JsonNull: typeof JsonNull
  };

  export type NullableJsonNullValueInput = (typeof NullableJsonNullValueInput)[keyof typeof NullableJsonNullValueInput]


  export const JsonNullValueInput: {
    JsonNull: typeof JsonNull
  };

  export type JsonNullValueInput = (typeof JsonNullValueInput)[keyof typeof JsonNullValueInput]


  export const QueryMode: {
    default: 'default',
    insensitive: 'insensitive'
  };

  export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


  export const JsonNullValueFilter: {
    DbNull: typeof DbNull,
    JsonNull: typeof JsonNull,
    AnyNull: typeof AnyNull
  };

  export type JsonNullValueFilter = (typeof JsonNullValueFilter)[keyof typeof JsonNullValueFilter]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  /**
   * Field references
   */


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'String[]'
   */
  export type ListStringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String[]'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'DateTime[]'
   */
  export type ListDateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime[]'>
    


  /**
   * Reference to a field of type 'Boolean'
   */
  export type BooleanFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Boolean'>
    


  /**
   * Reference to a field of type 'Json'
   */
  export type JsonFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Json'>
    


  /**
   * Reference to a field of type 'QueryMode'
   */
  export type EnumQueryModeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'QueryMode'>
    


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'Int[]'
   */
  export type ListIntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int[]'>
    


  /**
   * Reference to a field of type 'Float'
   */
  export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    


  /**
   * Reference to a field of type 'Float[]'
   */
  export type ListFloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float[]'>
    
  /**
   * Deep Input Types
   */


  export type usersWhereInput = {
    AND?: usersWhereInput | usersWhereInput[]
    OR?: usersWhereInput[]
    NOT?: usersWhereInput | usersWhereInput[]
    id?: UuidFilter<"users"> | string
    email?: StringNullableFilter<"users"> | string | null
    name?: StringNullableFilter<"users"> | string | null
    password?: StringNullableFilter<"users"> | string | null
    date_of_birth?: DateTimeNullableFilter<"users"> | Date | string | null
    gender?: StringNullableFilter<"users"> | string | null
    allergies?: StringNullableListFilter<"users">
    conditions?: StringNullableListFilter<"users">
    is_admin?: BoolNullableFilter<"users"> | boolean | null
    phone_number?: StringNullableFilter<"users"> | string | null
    emergency_contact?: JsonNullableFilter<"users">
    created_at?: DateTimeNullableFilter<"users"> | Date | string | null
    updated_at?: DateTimeNullableFilter<"users"> | Date | string | null
    subscription_status?: StringNullableFilter<"users"> | string | null
    subscription_plan?: StringNullableFilter<"users"> | string | null
    subscription_expires_at?: DateTimeNullableFilter<"users"> | Date | string | null
    subscription_features?: JsonNullableFilter<"users">
    adherence?: AdherenceListRelationFilter
    medication?: MedicationsListRelationFilter
    reminders?: RemindersListRelationFilter
    settings?: XOR<User_settingsNullableScalarRelationFilter, user_settingsWhereInput> | null
  }

  export type usersOrderByWithRelationInput = {
    id?: SortOrder
    email?: SortOrderInput | SortOrder
    name?: SortOrderInput | SortOrder
    password?: SortOrderInput | SortOrder
    date_of_birth?: SortOrderInput | SortOrder
    gender?: SortOrderInput | SortOrder
    allergies?: SortOrder
    conditions?: SortOrder
    is_admin?: SortOrderInput | SortOrder
    phone_number?: SortOrderInput | SortOrder
    emergency_contact?: SortOrderInput | SortOrder
    created_at?: SortOrderInput | SortOrder
    updated_at?: SortOrderInput | SortOrder
    subscription_status?: SortOrderInput | SortOrder
    subscription_plan?: SortOrderInput | SortOrder
    subscription_expires_at?: SortOrderInput | SortOrder
    subscription_features?: SortOrderInput | SortOrder
    adherence?: adherenceOrderByRelationAggregateInput
    medication?: medicationsOrderByRelationAggregateInput
    reminders?: remindersOrderByRelationAggregateInput
    settings?: user_settingsOrderByWithRelationInput
  }

  export type usersWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    email?: string
    AND?: usersWhereInput | usersWhereInput[]
    OR?: usersWhereInput[]
    NOT?: usersWhereInput | usersWhereInput[]
    name?: StringNullableFilter<"users"> | string | null
    password?: StringNullableFilter<"users"> | string | null
    date_of_birth?: DateTimeNullableFilter<"users"> | Date | string | null
    gender?: StringNullableFilter<"users"> | string | null
    allergies?: StringNullableListFilter<"users">
    conditions?: StringNullableListFilter<"users">
    is_admin?: BoolNullableFilter<"users"> | boolean | null
    phone_number?: StringNullableFilter<"users"> | string | null
    emergency_contact?: JsonNullableFilter<"users">
    created_at?: DateTimeNullableFilter<"users"> | Date | string | null
    updated_at?: DateTimeNullableFilter<"users"> | Date | string | null
    subscription_status?: StringNullableFilter<"users"> | string | null
    subscription_plan?: StringNullableFilter<"users"> | string | null
    subscription_expires_at?: DateTimeNullableFilter<"users"> | Date | string | null
    subscription_features?: JsonNullableFilter<"users">
    adherence?: AdherenceListRelationFilter
    medication?: MedicationsListRelationFilter
    reminders?: RemindersListRelationFilter
    settings?: XOR<User_settingsNullableScalarRelationFilter, user_settingsWhereInput> | null
  }, "id" | "email">

  export type usersOrderByWithAggregationInput = {
    id?: SortOrder
    email?: SortOrderInput | SortOrder
    name?: SortOrderInput | SortOrder
    password?: SortOrderInput | SortOrder
    date_of_birth?: SortOrderInput | SortOrder
    gender?: SortOrderInput | SortOrder
    allergies?: SortOrder
    conditions?: SortOrder
    is_admin?: SortOrderInput | SortOrder
    phone_number?: SortOrderInput | SortOrder
    emergency_contact?: SortOrderInput | SortOrder
    created_at?: SortOrderInput | SortOrder
    updated_at?: SortOrderInput | SortOrder
    subscription_status?: SortOrderInput | SortOrder
    subscription_plan?: SortOrderInput | SortOrder
    subscription_expires_at?: SortOrderInput | SortOrder
    subscription_features?: SortOrderInput | SortOrder
    _count?: usersCountOrderByAggregateInput
    _max?: usersMaxOrderByAggregateInput
    _min?: usersMinOrderByAggregateInput
  }

  export type usersScalarWhereWithAggregatesInput = {
    AND?: usersScalarWhereWithAggregatesInput | usersScalarWhereWithAggregatesInput[]
    OR?: usersScalarWhereWithAggregatesInput[]
    NOT?: usersScalarWhereWithAggregatesInput | usersScalarWhereWithAggregatesInput[]
    id?: UuidWithAggregatesFilter<"users"> | string
    email?: StringNullableWithAggregatesFilter<"users"> | string | null
    name?: StringNullableWithAggregatesFilter<"users"> | string | null
    password?: StringNullableWithAggregatesFilter<"users"> | string | null
    date_of_birth?: DateTimeNullableWithAggregatesFilter<"users"> | Date | string | null
    gender?: StringNullableWithAggregatesFilter<"users"> | string | null
    allergies?: StringNullableListFilter<"users">
    conditions?: StringNullableListFilter<"users">
    is_admin?: BoolNullableWithAggregatesFilter<"users"> | boolean | null
    phone_number?: StringNullableWithAggregatesFilter<"users"> | string | null
    emergency_contact?: JsonNullableWithAggregatesFilter<"users">
    created_at?: DateTimeNullableWithAggregatesFilter<"users"> | Date | string | null
    updated_at?: DateTimeNullableWithAggregatesFilter<"users"> | Date | string | null
    subscription_status?: StringNullableWithAggregatesFilter<"users"> | string | null
    subscription_plan?: StringNullableWithAggregatesFilter<"users"> | string | null
    subscription_expires_at?: DateTimeNullableWithAggregatesFilter<"users"> | Date | string | null
    subscription_features?: JsonNullableWithAggregatesFilter<"users">
  }

  export type user_settingsWhereInput = {
    AND?: user_settingsWhereInput | user_settingsWhereInput[]
    OR?: user_settingsWhereInput[]
    NOT?: user_settingsWhereInput | user_settingsWhereInput[]
    id?: UuidFilter<"user_settings"> | string
    user_id?: UuidFilter<"user_settings"> | string
    email_enabled?: BoolFilter<"user_settings"> | boolean
    preferred_times?: StringNullableListFilter<"user_settings">
    timezone?: StringFilter<"user_settings"> | string
    notification_preferences?: JsonNullableFilter<"user_settings">
    created_at?: DateTimeNullableFilter<"user_settings"> | Date | string | null
    updated_at?: DateTimeNullableFilter<"user_settings"> | Date | string | null
    user?: XOR<UsersScalarRelationFilter, usersWhereInput>
  }

  export type user_settingsOrderByWithRelationInput = {
    id?: SortOrder
    user_id?: SortOrder
    email_enabled?: SortOrder
    preferred_times?: SortOrder
    timezone?: SortOrder
    notification_preferences?: SortOrderInput | SortOrder
    created_at?: SortOrderInput | SortOrder
    updated_at?: SortOrderInput | SortOrder
    user?: usersOrderByWithRelationInput
  }

  export type user_settingsWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    user_id?: string
    AND?: user_settingsWhereInput | user_settingsWhereInput[]
    OR?: user_settingsWhereInput[]
    NOT?: user_settingsWhereInput | user_settingsWhereInput[]
    email_enabled?: BoolFilter<"user_settings"> | boolean
    preferred_times?: StringNullableListFilter<"user_settings">
    timezone?: StringFilter<"user_settings"> | string
    notification_preferences?: JsonNullableFilter<"user_settings">
    created_at?: DateTimeNullableFilter<"user_settings"> | Date | string | null
    updated_at?: DateTimeNullableFilter<"user_settings"> | Date | string | null
    user?: XOR<UsersScalarRelationFilter, usersWhereInput>
  }, "id" | "user_id">

  export type user_settingsOrderByWithAggregationInput = {
    id?: SortOrder
    user_id?: SortOrder
    email_enabled?: SortOrder
    preferred_times?: SortOrder
    timezone?: SortOrder
    notification_preferences?: SortOrderInput | SortOrder
    created_at?: SortOrderInput | SortOrder
    updated_at?: SortOrderInput | SortOrder
    _count?: user_settingsCountOrderByAggregateInput
    _max?: user_settingsMaxOrderByAggregateInput
    _min?: user_settingsMinOrderByAggregateInput
  }

  export type user_settingsScalarWhereWithAggregatesInput = {
    AND?: user_settingsScalarWhereWithAggregatesInput | user_settingsScalarWhereWithAggregatesInput[]
    OR?: user_settingsScalarWhereWithAggregatesInput[]
    NOT?: user_settingsScalarWhereWithAggregatesInput | user_settingsScalarWhereWithAggregatesInput[]
    id?: UuidWithAggregatesFilter<"user_settings"> | string
    user_id?: UuidWithAggregatesFilter<"user_settings"> | string
    email_enabled?: BoolWithAggregatesFilter<"user_settings"> | boolean
    preferred_times?: StringNullableListFilter<"user_settings">
    timezone?: StringWithAggregatesFilter<"user_settings"> | string
    notification_preferences?: JsonNullableWithAggregatesFilter<"user_settings">
    created_at?: DateTimeNullableWithAggregatesFilter<"user_settings"> | Date | string | null
    updated_at?: DateTimeNullableWithAggregatesFilter<"user_settings"> | Date | string | null
  }

  export type medicationsWhereInput = {
    AND?: medicationsWhereInput | medicationsWhereInput[]
    OR?: medicationsWhereInput[]
    NOT?: medicationsWhereInput | medicationsWhereInput[]
    id?: UuidFilter<"medications"> | string
    user_id?: UuidFilter<"medications"> | string
    name?: StringFilter<"medications"> | string
    dosage?: JsonFilter<"medications">
    frequency?: JsonFilter<"medications">
    scheduled_times?: StringNullableListFilter<"medications">
    instructions?: StringNullableFilter<"medications"> | string | null
    start_date?: DateTimeNullableFilter<"medications"> | Date | string | null
    end_date?: DateTimeNullableFilter<"medications"> | Date | string | null
    refill_reminder?: JsonNullableFilter<"medications">
    side_effects_to_watch?: StringNullableListFilter<"medications">
    active?: BoolNullableFilter<"medications"> | boolean | null
    medication_type?: StringNullableFilter<"medications"> | string | null
    image_url?: StringNullableFilter<"medications"> | string | null
    created_at?: DateTimeNullableFilter<"medications"> | Date | string | null
    updated_at?: DateTimeNullableFilter<"medications"> | Date | string | null
    adherence?: AdherenceListRelationFilter
    user?: XOR<UsersScalarRelationFilter, usersWhereInput>
    reminders?: RemindersListRelationFilter
  }

  export type medicationsOrderByWithRelationInput = {
    id?: SortOrder
    user_id?: SortOrder
    name?: SortOrder
    dosage?: SortOrder
    frequency?: SortOrder
    scheduled_times?: SortOrder
    instructions?: SortOrderInput | SortOrder
    start_date?: SortOrderInput | SortOrder
    end_date?: SortOrderInput | SortOrder
    refill_reminder?: SortOrderInput | SortOrder
    side_effects_to_watch?: SortOrder
    active?: SortOrderInput | SortOrder
    medication_type?: SortOrderInput | SortOrder
    image_url?: SortOrderInput | SortOrder
    created_at?: SortOrderInput | SortOrder
    updated_at?: SortOrderInput | SortOrder
    adherence?: adherenceOrderByRelationAggregateInput
    user?: usersOrderByWithRelationInput
    reminders?: remindersOrderByRelationAggregateInput
  }

  export type medicationsWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: medicationsWhereInput | medicationsWhereInput[]
    OR?: medicationsWhereInput[]
    NOT?: medicationsWhereInput | medicationsWhereInput[]
    user_id?: UuidFilter<"medications"> | string
    name?: StringFilter<"medications"> | string
    dosage?: JsonFilter<"medications">
    frequency?: JsonFilter<"medications">
    scheduled_times?: StringNullableListFilter<"medications">
    instructions?: StringNullableFilter<"medications"> | string | null
    start_date?: DateTimeNullableFilter<"medications"> | Date | string | null
    end_date?: DateTimeNullableFilter<"medications"> | Date | string | null
    refill_reminder?: JsonNullableFilter<"medications">
    side_effects_to_watch?: StringNullableListFilter<"medications">
    active?: BoolNullableFilter<"medications"> | boolean | null
    medication_type?: StringNullableFilter<"medications"> | string | null
    image_url?: StringNullableFilter<"medications"> | string | null
    created_at?: DateTimeNullableFilter<"medications"> | Date | string | null
    updated_at?: DateTimeNullableFilter<"medications"> | Date | string | null
    adherence?: AdherenceListRelationFilter
    user?: XOR<UsersScalarRelationFilter, usersWhereInput>
    reminders?: RemindersListRelationFilter
  }, "id">

  export type medicationsOrderByWithAggregationInput = {
    id?: SortOrder
    user_id?: SortOrder
    name?: SortOrder
    dosage?: SortOrder
    frequency?: SortOrder
    scheduled_times?: SortOrder
    instructions?: SortOrderInput | SortOrder
    start_date?: SortOrderInput | SortOrder
    end_date?: SortOrderInput | SortOrder
    refill_reminder?: SortOrderInput | SortOrder
    side_effects_to_watch?: SortOrder
    active?: SortOrderInput | SortOrder
    medication_type?: SortOrderInput | SortOrder
    image_url?: SortOrderInput | SortOrder
    created_at?: SortOrderInput | SortOrder
    updated_at?: SortOrderInput | SortOrder
    _count?: medicationsCountOrderByAggregateInput
    _max?: medicationsMaxOrderByAggregateInput
    _min?: medicationsMinOrderByAggregateInput
  }

  export type medicationsScalarWhereWithAggregatesInput = {
    AND?: medicationsScalarWhereWithAggregatesInput | medicationsScalarWhereWithAggregatesInput[]
    OR?: medicationsScalarWhereWithAggregatesInput[]
    NOT?: medicationsScalarWhereWithAggregatesInput | medicationsScalarWhereWithAggregatesInput[]
    id?: UuidWithAggregatesFilter<"medications"> | string
    user_id?: UuidWithAggregatesFilter<"medications"> | string
    name?: StringWithAggregatesFilter<"medications"> | string
    dosage?: JsonWithAggregatesFilter<"medications">
    frequency?: JsonWithAggregatesFilter<"medications">
    scheduled_times?: StringNullableListFilter<"medications">
    instructions?: StringNullableWithAggregatesFilter<"medications"> | string | null
    start_date?: DateTimeNullableWithAggregatesFilter<"medications"> | Date | string | null
    end_date?: DateTimeNullableWithAggregatesFilter<"medications"> | Date | string | null
    refill_reminder?: JsonNullableWithAggregatesFilter<"medications">
    side_effects_to_watch?: StringNullableListFilter<"medications">
    active?: BoolNullableWithAggregatesFilter<"medications"> | boolean | null
    medication_type?: StringNullableWithAggregatesFilter<"medications"> | string | null
    image_url?: StringNullableWithAggregatesFilter<"medications"> | string | null
    created_at?: DateTimeNullableWithAggregatesFilter<"medications"> | Date | string | null
    updated_at?: DateTimeNullableWithAggregatesFilter<"medications"> | Date | string | null
  }

  export type adherenceWhereInput = {
    AND?: adherenceWhereInput | adherenceWhereInput[]
    OR?: adherenceWhereInput[]
    NOT?: adherenceWhereInput | adherenceWhereInput[]
    id?: UuidFilter<"adherence"> | string
    user_id?: UuidFilter<"adherence"> | string
    medication_id?: UuidFilter<"adherence"> | string
    scheduled_time?: StringFilter<"adherence"> | string
    scheduled_date?: DateTimeFilter<"adherence"> | Date | string
    taken_time?: DateTimeNullableFilter<"adherence"> | Date | string | null
    status?: StringNullableFilter<"adherence"> | string | null
    notes?: StringNullableFilter<"adherence"> | string | null
    reminder_sent?: BoolNullableFilter<"adherence"> | boolean | null
    side_effects_reported?: StringNullableListFilter<"adherence">
    dosage_taken?: JsonNullableFilter<"adherence">
    created_at?: DateTimeNullableFilter<"adherence"> | Date | string | null
    updated_at?: DateTimeNullableFilter<"adherence"> | Date | string | null
    medication?: XOR<MedicationsScalarRelationFilter, medicationsWhereInput>
    user?: XOR<UsersScalarRelationFilter, usersWhereInput>
    reminders?: RemindersListRelationFilter
  }

  export type adherenceOrderByWithRelationInput = {
    id?: SortOrder
    user_id?: SortOrder
    medication_id?: SortOrder
    scheduled_time?: SortOrder
    scheduled_date?: SortOrder
    taken_time?: SortOrderInput | SortOrder
    status?: SortOrderInput | SortOrder
    notes?: SortOrderInput | SortOrder
    reminder_sent?: SortOrderInput | SortOrder
    side_effects_reported?: SortOrder
    dosage_taken?: SortOrderInput | SortOrder
    created_at?: SortOrderInput | SortOrder
    updated_at?: SortOrderInput | SortOrder
    medication?: medicationsOrderByWithRelationInput
    user?: usersOrderByWithRelationInput
    reminders?: remindersOrderByRelationAggregateInput
  }

  export type adherenceWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: adherenceWhereInput | adherenceWhereInput[]
    OR?: adherenceWhereInput[]
    NOT?: adherenceWhereInput | adherenceWhereInput[]
    user_id?: UuidFilter<"adherence"> | string
    medication_id?: UuidFilter<"adherence"> | string
    scheduled_time?: StringFilter<"adherence"> | string
    scheduled_date?: DateTimeFilter<"adherence"> | Date | string
    taken_time?: DateTimeNullableFilter<"adherence"> | Date | string | null
    status?: StringNullableFilter<"adherence"> | string | null
    notes?: StringNullableFilter<"adherence"> | string | null
    reminder_sent?: BoolNullableFilter<"adherence"> | boolean | null
    side_effects_reported?: StringNullableListFilter<"adherence">
    dosage_taken?: JsonNullableFilter<"adherence">
    created_at?: DateTimeNullableFilter<"adherence"> | Date | string | null
    updated_at?: DateTimeNullableFilter<"adherence"> | Date | string | null
    medication?: XOR<MedicationsScalarRelationFilter, medicationsWhereInput>
    user?: XOR<UsersScalarRelationFilter, usersWhereInput>
    reminders?: RemindersListRelationFilter
  }, "id">

  export type adherenceOrderByWithAggregationInput = {
    id?: SortOrder
    user_id?: SortOrder
    medication_id?: SortOrder
    scheduled_time?: SortOrder
    scheduled_date?: SortOrder
    taken_time?: SortOrderInput | SortOrder
    status?: SortOrderInput | SortOrder
    notes?: SortOrderInput | SortOrder
    reminder_sent?: SortOrderInput | SortOrder
    side_effects_reported?: SortOrder
    dosage_taken?: SortOrderInput | SortOrder
    created_at?: SortOrderInput | SortOrder
    updated_at?: SortOrderInput | SortOrder
    _count?: adherenceCountOrderByAggregateInput
    _max?: adherenceMaxOrderByAggregateInput
    _min?: adherenceMinOrderByAggregateInput
  }

  export type adherenceScalarWhereWithAggregatesInput = {
    AND?: adherenceScalarWhereWithAggregatesInput | adherenceScalarWhereWithAggregatesInput[]
    OR?: adherenceScalarWhereWithAggregatesInput[]
    NOT?: adherenceScalarWhereWithAggregatesInput | adherenceScalarWhereWithAggregatesInput[]
    id?: UuidWithAggregatesFilter<"adherence"> | string
    user_id?: UuidWithAggregatesFilter<"adherence"> | string
    medication_id?: UuidWithAggregatesFilter<"adherence"> | string
    scheduled_time?: StringWithAggregatesFilter<"adherence"> | string
    scheduled_date?: DateTimeWithAggregatesFilter<"adherence"> | Date | string
    taken_time?: DateTimeNullableWithAggregatesFilter<"adherence"> | Date | string | null
    status?: StringNullableWithAggregatesFilter<"adherence"> | string | null
    notes?: StringNullableWithAggregatesFilter<"adherence"> | string | null
    reminder_sent?: BoolNullableWithAggregatesFilter<"adherence"> | boolean | null
    side_effects_reported?: StringNullableListFilter<"adherence">
    dosage_taken?: JsonNullableWithAggregatesFilter<"adherence">
    created_at?: DateTimeNullableWithAggregatesFilter<"adherence"> | Date | string | null
    updated_at?: DateTimeNullableWithAggregatesFilter<"adherence"> | Date | string | null
  }

  export type remindersWhereInput = {
    AND?: remindersWhereInput | remindersWhereInput[]
    OR?: remindersWhereInput[]
    NOT?: remindersWhereInput | remindersWhereInput[]
    id?: UuidFilter<"reminders"> | string
    user_id?: UuidFilter<"reminders"> | string
    medication_id?: UuidFilter<"reminders"> | string
    scheduled_time?: StringFilter<"reminders"> | string
    scheduled_date?: DateTimeFilter<"reminders"> | Date | string
    status?: StringNullableFilter<"reminders"> | string | null
    channels?: JsonNullableFilter<"reminders">
    message?: StringNullableFilter<"reminders"> | string | null
    retry_count?: IntNullableFilter<"reminders"> | number | null
    last_retry?: DateTimeNullableFilter<"reminders"> | Date | string | null
    adherence_id?: UuidNullableFilter<"reminders"> | string | null
    created_at?: DateTimeNullableFilter<"reminders"> | Date | string | null
    updated_at?: DateTimeNullableFilter<"reminders"> | Date | string | null
    adherence?: XOR<AdherenceNullableScalarRelationFilter, adherenceWhereInput> | null
    medication?: XOR<MedicationsScalarRelationFilter, medicationsWhereInput>
    user?: XOR<UsersScalarRelationFilter, usersWhereInput>
  }

  export type remindersOrderByWithRelationInput = {
    id?: SortOrder
    user_id?: SortOrder
    medication_id?: SortOrder
    scheduled_time?: SortOrder
    scheduled_date?: SortOrder
    status?: SortOrderInput | SortOrder
    channels?: SortOrderInput | SortOrder
    message?: SortOrderInput | SortOrder
    retry_count?: SortOrderInput | SortOrder
    last_retry?: SortOrderInput | SortOrder
    adherence_id?: SortOrderInput | SortOrder
    created_at?: SortOrderInput | SortOrder
    updated_at?: SortOrderInput | SortOrder
    adherence?: adherenceOrderByWithRelationInput
    medication?: medicationsOrderByWithRelationInput
    user?: usersOrderByWithRelationInput
  }

  export type remindersWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: remindersWhereInput | remindersWhereInput[]
    OR?: remindersWhereInput[]
    NOT?: remindersWhereInput | remindersWhereInput[]
    user_id?: UuidFilter<"reminders"> | string
    medication_id?: UuidFilter<"reminders"> | string
    scheduled_time?: StringFilter<"reminders"> | string
    scheduled_date?: DateTimeFilter<"reminders"> | Date | string
    status?: StringNullableFilter<"reminders"> | string | null
    channels?: JsonNullableFilter<"reminders">
    message?: StringNullableFilter<"reminders"> | string | null
    retry_count?: IntNullableFilter<"reminders"> | number | null
    last_retry?: DateTimeNullableFilter<"reminders"> | Date | string | null
    adherence_id?: UuidNullableFilter<"reminders"> | string | null
    created_at?: DateTimeNullableFilter<"reminders"> | Date | string | null
    updated_at?: DateTimeNullableFilter<"reminders"> | Date | string | null
    adherence?: XOR<AdherenceNullableScalarRelationFilter, adherenceWhereInput> | null
    medication?: XOR<MedicationsScalarRelationFilter, medicationsWhereInput>
    user?: XOR<UsersScalarRelationFilter, usersWhereInput>
  }, "id">

  export type remindersOrderByWithAggregationInput = {
    id?: SortOrder
    user_id?: SortOrder
    medication_id?: SortOrder
    scheduled_time?: SortOrder
    scheduled_date?: SortOrder
    status?: SortOrderInput | SortOrder
    channels?: SortOrderInput | SortOrder
    message?: SortOrderInput | SortOrder
    retry_count?: SortOrderInput | SortOrder
    last_retry?: SortOrderInput | SortOrder
    adherence_id?: SortOrderInput | SortOrder
    created_at?: SortOrderInput | SortOrder
    updated_at?: SortOrderInput | SortOrder
    _count?: remindersCountOrderByAggregateInput
    _avg?: remindersAvgOrderByAggregateInput
    _max?: remindersMaxOrderByAggregateInput
    _min?: remindersMinOrderByAggregateInput
    _sum?: remindersSumOrderByAggregateInput
  }

  export type remindersScalarWhereWithAggregatesInput = {
    AND?: remindersScalarWhereWithAggregatesInput | remindersScalarWhereWithAggregatesInput[]
    OR?: remindersScalarWhereWithAggregatesInput[]
    NOT?: remindersScalarWhereWithAggregatesInput | remindersScalarWhereWithAggregatesInput[]
    id?: UuidWithAggregatesFilter<"reminders"> | string
    user_id?: UuidWithAggregatesFilter<"reminders"> | string
    medication_id?: UuidWithAggregatesFilter<"reminders"> | string
    scheduled_time?: StringWithAggregatesFilter<"reminders"> | string
    scheduled_date?: DateTimeWithAggregatesFilter<"reminders"> | Date | string
    status?: StringNullableWithAggregatesFilter<"reminders"> | string | null
    channels?: JsonNullableWithAggregatesFilter<"reminders">
    message?: StringNullableWithAggregatesFilter<"reminders"> | string | null
    retry_count?: IntNullableWithAggregatesFilter<"reminders"> | number | null
    last_retry?: DateTimeNullableWithAggregatesFilter<"reminders"> | Date | string | null
    adherence_id?: UuidNullableWithAggregatesFilter<"reminders"> | string | null
    created_at?: DateTimeNullableWithAggregatesFilter<"reminders"> | Date | string | null
    updated_at?: DateTimeNullableWithAggregatesFilter<"reminders"> | Date | string | null
  }

  export type usersCreateInput = {
    id?: string
    email?: string | null
    name?: string | null
    password?: string | null
    date_of_birth?: Date | string | null
    gender?: string | null
    allergies?: usersCreateallergiesInput | string[]
    conditions?: usersCreateconditionsInput | string[]
    is_admin?: boolean | null
    phone_number?: string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
    subscription_status?: string | null
    subscription_plan?: string | null
    subscription_expires_at?: Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
    adherence?: adherenceCreateNestedManyWithoutUserInput
    medication?: medicationsCreateNestedManyWithoutUserInput
    reminders?: remindersCreateNestedManyWithoutUserInput
    settings?: user_settingsCreateNestedOneWithoutUserInput
  }

  export type usersUncheckedCreateInput = {
    id?: string
    email?: string | null
    name?: string | null
    password?: string | null
    date_of_birth?: Date | string | null
    gender?: string | null
    allergies?: usersCreateallergiesInput | string[]
    conditions?: usersCreateconditionsInput | string[]
    is_admin?: boolean | null
    phone_number?: string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
    subscription_status?: string | null
    subscription_plan?: string | null
    subscription_expires_at?: Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
    adherence?: adherenceUncheckedCreateNestedManyWithoutUserInput
    medication?: medicationsUncheckedCreateNestedManyWithoutUserInput
    reminders?: remindersUncheckedCreateNestedManyWithoutUserInput
    settings?: user_settingsUncheckedCreateNestedOneWithoutUserInput
  }

  export type usersUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    date_of_birth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    gender?: NullableStringFieldUpdateOperationsInput | string | null
    allergies?: usersUpdateallergiesInput | string[]
    conditions?: usersUpdateconditionsInput | string[]
    is_admin?: NullableBoolFieldUpdateOperationsInput | boolean | null
    phone_number?: NullableStringFieldUpdateOperationsInput | string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_status?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_plan?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_expires_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
    adherence?: adherenceUpdateManyWithoutUserNestedInput
    medication?: medicationsUpdateManyWithoutUserNestedInput
    reminders?: remindersUpdateManyWithoutUserNestedInput
    settings?: user_settingsUpdateOneWithoutUserNestedInput
  }

  export type usersUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    date_of_birth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    gender?: NullableStringFieldUpdateOperationsInput | string | null
    allergies?: usersUpdateallergiesInput | string[]
    conditions?: usersUpdateconditionsInput | string[]
    is_admin?: NullableBoolFieldUpdateOperationsInput | boolean | null
    phone_number?: NullableStringFieldUpdateOperationsInput | string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_status?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_plan?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_expires_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
    adherence?: adherenceUncheckedUpdateManyWithoutUserNestedInput
    medication?: medicationsUncheckedUpdateManyWithoutUserNestedInput
    reminders?: remindersUncheckedUpdateManyWithoutUserNestedInput
    settings?: user_settingsUncheckedUpdateOneWithoutUserNestedInput
  }

  export type usersCreateManyInput = {
    id?: string
    email?: string | null
    name?: string | null
    password?: string | null
    date_of_birth?: Date | string | null
    gender?: string | null
    allergies?: usersCreateallergiesInput | string[]
    conditions?: usersCreateconditionsInput | string[]
    is_admin?: boolean | null
    phone_number?: string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
    subscription_status?: string | null
    subscription_plan?: string | null
    subscription_expires_at?: Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
  }

  export type usersUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    date_of_birth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    gender?: NullableStringFieldUpdateOperationsInput | string | null
    allergies?: usersUpdateallergiesInput | string[]
    conditions?: usersUpdateconditionsInput | string[]
    is_admin?: NullableBoolFieldUpdateOperationsInput | boolean | null
    phone_number?: NullableStringFieldUpdateOperationsInput | string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_status?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_plan?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_expires_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
  }

  export type usersUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    date_of_birth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    gender?: NullableStringFieldUpdateOperationsInput | string | null
    allergies?: usersUpdateallergiesInput | string[]
    conditions?: usersUpdateconditionsInput | string[]
    is_admin?: NullableBoolFieldUpdateOperationsInput | boolean | null
    phone_number?: NullableStringFieldUpdateOperationsInput | string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_status?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_plan?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_expires_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
  }

  export type user_settingsCreateInput = {
    id?: string
    email_enabled: boolean
    preferred_times?: user_settingsCreatepreferred_timesInput | string[]
    timezone: string
    notification_preferences?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
    user: usersCreateNestedOneWithoutSettingsInput
  }

  export type user_settingsUncheckedCreateInput = {
    id?: string
    user_id: string
    email_enabled: boolean
    preferred_times?: user_settingsCreatepreferred_timesInput | string[]
    timezone: string
    notification_preferences?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
  }

  export type user_settingsUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    email_enabled?: BoolFieldUpdateOperationsInput | boolean
    preferred_times?: user_settingsUpdatepreferred_timesInput | string[]
    timezone?: StringFieldUpdateOperationsInput | string
    notification_preferences?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    user?: usersUpdateOneRequiredWithoutSettingsNestedInput
  }

  export type user_settingsUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    user_id?: StringFieldUpdateOperationsInput | string
    email_enabled?: BoolFieldUpdateOperationsInput | boolean
    preferred_times?: user_settingsUpdatepreferred_timesInput | string[]
    timezone?: StringFieldUpdateOperationsInput | string
    notification_preferences?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type user_settingsCreateManyInput = {
    id?: string
    user_id: string
    email_enabled: boolean
    preferred_times?: user_settingsCreatepreferred_timesInput | string[]
    timezone: string
    notification_preferences?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
  }

  export type user_settingsUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    email_enabled?: BoolFieldUpdateOperationsInput | boolean
    preferred_times?: user_settingsUpdatepreferred_timesInput | string[]
    timezone?: StringFieldUpdateOperationsInput | string
    notification_preferences?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type user_settingsUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    user_id?: StringFieldUpdateOperationsInput | string
    email_enabled?: BoolFieldUpdateOperationsInput | boolean
    preferred_times?: user_settingsUpdatepreferred_timesInput | string[]
    timezone?: StringFieldUpdateOperationsInput | string
    notification_preferences?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type medicationsCreateInput = {
    id?: string
    name: string
    dosage: JsonNullValueInput | InputJsonValue
    frequency: JsonNullValueInput | InputJsonValue
    scheduled_times?: medicationsCreatescheduled_timesInput | string[]
    instructions?: string | null
    start_date?: Date | string | null
    end_date?: Date | string | null
    refill_reminder?: NullableJsonNullValueInput | InputJsonValue
    side_effects_to_watch?: medicationsCreateside_effects_to_watchInput | string[]
    active?: boolean | null
    medication_type?: string | null
    image_url?: string | null
    created_at?: Date | string | null
    updated_at?: Date | string | null
    adherence?: adherenceCreateNestedManyWithoutMedicationInput
    user: usersCreateNestedOneWithoutMedicationInput
    reminders?: remindersCreateNestedManyWithoutMedicationInput
  }

  export type medicationsUncheckedCreateInput = {
    id?: string
    user_id: string
    name: string
    dosage: JsonNullValueInput | InputJsonValue
    frequency: JsonNullValueInput | InputJsonValue
    scheduled_times?: medicationsCreatescheduled_timesInput | string[]
    instructions?: string | null
    start_date?: Date | string | null
    end_date?: Date | string | null
    refill_reminder?: NullableJsonNullValueInput | InputJsonValue
    side_effects_to_watch?: medicationsCreateside_effects_to_watchInput | string[]
    active?: boolean | null
    medication_type?: string | null
    image_url?: string | null
    created_at?: Date | string | null
    updated_at?: Date | string | null
    adherence?: adherenceUncheckedCreateNestedManyWithoutMedicationInput
    reminders?: remindersUncheckedCreateNestedManyWithoutMedicationInput
  }

  export type medicationsUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dosage?: JsonNullValueInput | InputJsonValue
    frequency?: JsonNullValueInput | InputJsonValue
    scheduled_times?: medicationsUpdatescheduled_timesInput | string[]
    instructions?: NullableStringFieldUpdateOperationsInput | string | null
    start_date?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    end_date?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    refill_reminder?: NullableJsonNullValueInput | InputJsonValue
    side_effects_to_watch?: medicationsUpdateside_effects_to_watchInput | string[]
    active?: NullableBoolFieldUpdateOperationsInput | boolean | null
    medication_type?: NullableStringFieldUpdateOperationsInput | string | null
    image_url?: NullableStringFieldUpdateOperationsInput | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    adherence?: adherenceUpdateManyWithoutMedicationNestedInput
    user?: usersUpdateOneRequiredWithoutMedicationNestedInput
    reminders?: remindersUpdateManyWithoutMedicationNestedInput
  }

  export type medicationsUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    user_id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dosage?: JsonNullValueInput | InputJsonValue
    frequency?: JsonNullValueInput | InputJsonValue
    scheduled_times?: medicationsUpdatescheduled_timesInput | string[]
    instructions?: NullableStringFieldUpdateOperationsInput | string | null
    start_date?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    end_date?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    refill_reminder?: NullableJsonNullValueInput | InputJsonValue
    side_effects_to_watch?: medicationsUpdateside_effects_to_watchInput | string[]
    active?: NullableBoolFieldUpdateOperationsInput | boolean | null
    medication_type?: NullableStringFieldUpdateOperationsInput | string | null
    image_url?: NullableStringFieldUpdateOperationsInput | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    adherence?: adherenceUncheckedUpdateManyWithoutMedicationNestedInput
    reminders?: remindersUncheckedUpdateManyWithoutMedicationNestedInput
  }

  export type medicationsCreateManyInput = {
    id?: string
    user_id: string
    name: string
    dosage: JsonNullValueInput | InputJsonValue
    frequency: JsonNullValueInput | InputJsonValue
    scheduled_times?: medicationsCreatescheduled_timesInput | string[]
    instructions?: string | null
    start_date?: Date | string | null
    end_date?: Date | string | null
    refill_reminder?: NullableJsonNullValueInput | InputJsonValue
    side_effects_to_watch?: medicationsCreateside_effects_to_watchInput | string[]
    active?: boolean | null
    medication_type?: string | null
    image_url?: string | null
    created_at?: Date | string | null
    updated_at?: Date | string | null
  }

  export type medicationsUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dosage?: JsonNullValueInput | InputJsonValue
    frequency?: JsonNullValueInput | InputJsonValue
    scheduled_times?: medicationsUpdatescheduled_timesInput | string[]
    instructions?: NullableStringFieldUpdateOperationsInput | string | null
    start_date?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    end_date?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    refill_reminder?: NullableJsonNullValueInput | InputJsonValue
    side_effects_to_watch?: medicationsUpdateside_effects_to_watchInput | string[]
    active?: NullableBoolFieldUpdateOperationsInput | boolean | null
    medication_type?: NullableStringFieldUpdateOperationsInput | string | null
    image_url?: NullableStringFieldUpdateOperationsInput | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type medicationsUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    user_id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dosage?: JsonNullValueInput | InputJsonValue
    frequency?: JsonNullValueInput | InputJsonValue
    scheduled_times?: medicationsUpdatescheduled_timesInput | string[]
    instructions?: NullableStringFieldUpdateOperationsInput | string | null
    start_date?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    end_date?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    refill_reminder?: NullableJsonNullValueInput | InputJsonValue
    side_effects_to_watch?: medicationsUpdateside_effects_to_watchInput | string[]
    active?: NullableBoolFieldUpdateOperationsInput | boolean | null
    medication_type?: NullableStringFieldUpdateOperationsInput | string | null
    image_url?: NullableStringFieldUpdateOperationsInput | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type adherenceCreateInput = {
    id?: string
    scheduled_time: string
    scheduled_date: Date | string
    taken_time?: Date | string | null
    status?: string | null
    notes?: string | null
    reminder_sent?: boolean | null
    side_effects_reported?: adherenceCreateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
    medication: medicationsCreateNestedOneWithoutAdherenceInput
    user: usersCreateNestedOneWithoutAdherenceInput
    reminders?: remindersCreateNestedManyWithoutAdherenceInput
  }

  export type adherenceUncheckedCreateInput = {
    id?: string
    user_id: string
    medication_id: string
    scheduled_time: string
    scheduled_date: Date | string
    taken_time?: Date | string | null
    status?: string | null
    notes?: string | null
    reminder_sent?: boolean | null
    side_effects_reported?: adherenceCreateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
    reminders?: remindersUncheckedCreateNestedManyWithoutAdherenceInput
  }

  export type adherenceUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    taken_time?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    status?: NullableStringFieldUpdateOperationsInput | string | null
    notes?: NullableStringFieldUpdateOperationsInput | string | null
    reminder_sent?: NullableBoolFieldUpdateOperationsInput | boolean | null
    side_effects_reported?: adherenceUpdateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    medication?: medicationsUpdateOneRequiredWithoutAdherenceNestedInput
    user?: usersUpdateOneRequiredWithoutAdherenceNestedInput
    reminders?: remindersUpdateManyWithoutAdherenceNestedInput
  }

  export type adherenceUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    user_id?: StringFieldUpdateOperationsInput | string
    medication_id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    taken_time?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    status?: NullableStringFieldUpdateOperationsInput | string | null
    notes?: NullableStringFieldUpdateOperationsInput | string | null
    reminder_sent?: NullableBoolFieldUpdateOperationsInput | boolean | null
    side_effects_reported?: adherenceUpdateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    reminders?: remindersUncheckedUpdateManyWithoutAdherenceNestedInput
  }

  export type adherenceCreateManyInput = {
    id?: string
    user_id: string
    medication_id: string
    scheduled_time: string
    scheduled_date: Date | string
    taken_time?: Date | string | null
    status?: string | null
    notes?: string | null
    reminder_sent?: boolean | null
    side_effects_reported?: adherenceCreateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
  }

  export type adherenceUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    taken_time?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    status?: NullableStringFieldUpdateOperationsInput | string | null
    notes?: NullableStringFieldUpdateOperationsInput | string | null
    reminder_sent?: NullableBoolFieldUpdateOperationsInput | boolean | null
    side_effects_reported?: adherenceUpdateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type adherenceUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    user_id?: StringFieldUpdateOperationsInput | string
    medication_id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    taken_time?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    status?: NullableStringFieldUpdateOperationsInput | string | null
    notes?: NullableStringFieldUpdateOperationsInput | string | null
    reminder_sent?: NullableBoolFieldUpdateOperationsInput | boolean | null
    side_effects_reported?: adherenceUpdateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type remindersCreateInput = {
    id?: string
    scheduled_time: string
    scheduled_date: Date | string
    status?: string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: string | null
    retry_count?: number | null
    last_retry?: Date | string | null
    created_at?: Date | string | null
    updated_at?: Date | string | null
    adherence?: adherenceCreateNestedOneWithoutRemindersInput
    medication: medicationsCreateNestedOneWithoutRemindersInput
    user: usersCreateNestedOneWithoutRemindersInput
  }

  export type remindersUncheckedCreateInput = {
    id?: string
    user_id: string
    medication_id: string
    scheduled_time: string
    scheduled_date: Date | string
    status?: string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: string | null
    retry_count?: number | null
    last_retry?: Date | string | null
    adherence_id?: string | null
    created_at?: Date | string | null
    updated_at?: Date | string | null
  }

  export type remindersUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: NullableStringFieldUpdateOperationsInput | string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: NullableStringFieldUpdateOperationsInput | string | null
    retry_count?: NullableIntFieldUpdateOperationsInput | number | null
    last_retry?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    adherence?: adherenceUpdateOneWithoutRemindersNestedInput
    medication?: medicationsUpdateOneRequiredWithoutRemindersNestedInput
    user?: usersUpdateOneRequiredWithoutRemindersNestedInput
  }

  export type remindersUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    user_id?: StringFieldUpdateOperationsInput | string
    medication_id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: NullableStringFieldUpdateOperationsInput | string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: NullableStringFieldUpdateOperationsInput | string | null
    retry_count?: NullableIntFieldUpdateOperationsInput | number | null
    last_retry?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    adherence_id?: NullableStringFieldUpdateOperationsInput | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type remindersCreateManyInput = {
    id?: string
    user_id: string
    medication_id: string
    scheduled_time: string
    scheduled_date: Date | string
    status?: string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: string | null
    retry_count?: number | null
    last_retry?: Date | string | null
    adherence_id?: string | null
    created_at?: Date | string | null
    updated_at?: Date | string | null
  }

  export type remindersUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: NullableStringFieldUpdateOperationsInput | string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: NullableStringFieldUpdateOperationsInput | string | null
    retry_count?: NullableIntFieldUpdateOperationsInput | number | null
    last_retry?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type remindersUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    user_id?: StringFieldUpdateOperationsInput | string
    medication_id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: NullableStringFieldUpdateOperationsInput | string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: NullableStringFieldUpdateOperationsInput | string | null
    retry_count?: NullableIntFieldUpdateOperationsInput | number | null
    last_retry?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    adherence_id?: NullableStringFieldUpdateOperationsInput | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type UuidFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedUuidFilter<$PrismaModel> | string
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type DateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type StringNullableListFilter<$PrismaModel = never> = {
    equals?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    has?: string | StringFieldRefInput<$PrismaModel> | null
    hasEvery?: string[] | ListStringFieldRefInput<$PrismaModel>
    hasSome?: string[] | ListStringFieldRefInput<$PrismaModel>
    isEmpty?: boolean
  }

  export type BoolNullableFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel> | null
    not?: NestedBoolNullableFilter<$PrismaModel> | boolean | null
  }
  export type JsonNullableFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonNullableFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>

  export type JsonNullableFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type AdherenceListRelationFilter = {
    every?: adherenceWhereInput
    some?: adherenceWhereInput
    none?: adherenceWhereInput
  }

  export type MedicationsListRelationFilter = {
    every?: medicationsWhereInput
    some?: medicationsWhereInput
    none?: medicationsWhereInput
  }

  export type RemindersListRelationFilter = {
    every?: remindersWhereInput
    some?: remindersWhereInput
    none?: remindersWhereInput
  }

  export type User_settingsNullableScalarRelationFilter = {
    is?: user_settingsWhereInput | null
    isNot?: user_settingsWhereInput | null
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type adherenceOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type medicationsOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type remindersOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type usersCountOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    name?: SortOrder
    password?: SortOrder
    date_of_birth?: SortOrder
    gender?: SortOrder
    allergies?: SortOrder
    conditions?: SortOrder
    is_admin?: SortOrder
    phone_number?: SortOrder
    emergency_contact?: SortOrder
    created_at?: SortOrder
    updated_at?: SortOrder
    subscription_status?: SortOrder
    subscription_plan?: SortOrder
    subscription_expires_at?: SortOrder
    subscription_features?: SortOrder
  }

  export type usersMaxOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    name?: SortOrder
    password?: SortOrder
    date_of_birth?: SortOrder
    gender?: SortOrder
    is_admin?: SortOrder
    phone_number?: SortOrder
    created_at?: SortOrder
    updated_at?: SortOrder
    subscription_status?: SortOrder
    subscription_plan?: SortOrder
    subscription_expires_at?: SortOrder
  }

  export type usersMinOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    name?: SortOrder
    password?: SortOrder
    date_of_birth?: SortOrder
    gender?: SortOrder
    is_admin?: SortOrder
    phone_number?: SortOrder
    created_at?: SortOrder
    updated_at?: SortOrder
    subscription_status?: SortOrder
    subscription_plan?: SortOrder
    subscription_expires_at?: SortOrder
  }

  export type UuidWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedUuidWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type DateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type BoolNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel> | null
    not?: NestedBoolNullableWithAggregatesFilter<$PrismaModel> | boolean | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedBoolNullableFilter<$PrismaModel>
    _max?: NestedBoolNullableFilter<$PrismaModel>
  }
  export type JsonNullableWithAggregatesFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>

  export type JsonNullableWithAggregatesFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedJsonNullableFilter<$PrismaModel>
    _max?: NestedJsonNullableFilter<$PrismaModel>
  }

  export type BoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type UsersScalarRelationFilter = {
    is?: usersWhereInput
    isNot?: usersWhereInput
  }

  export type user_settingsCountOrderByAggregateInput = {
    id?: SortOrder
    user_id?: SortOrder
    email_enabled?: SortOrder
    preferred_times?: SortOrder
    timezone?: SortOrder
    notification_preferences?: SortOrder
    created_at?: SortOrder
    updated_at?: SortOrder
  }

  export type user_settingsMaxOrderByAggregateInput = {
    id?: SortOrder
    user_id?: SortOrder
    email_enabled?: SortOrder
    timezone?: SortOrder
    created_at?: SortOrder
    updated_at?: SortOrder
  }

  export type user_settingsMinOrderByAggregateInput = {
    id?: SortOrder
    user_id?: SortOrder
    email_enabled?: SortOrder
    timezone?: SortOrder
    created_at?: SortOrder
    updated_at?: SortOrder
  }

  export type BoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }
  export type JsonFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonFilterBase<$PrismaModel>>, 'path'>>

  export type JsonFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type medicationsCountOrderByAggregateInput = {
    id?: SortOrder
    user_id?: SortOrder
    name?: SortOrder
    dosage?: SortOrder
    frequency?: SortOrder
    scheduled_times?: SortOrder
    instructions?: SortOrder
    start_date?: SortOrder
    end_date?: SortOrder
    refill_reminder?: SortOrder
    side_effects_to_watch?: SortOrder
    active?: SortOrder
    medication_type?: SortOrder
    image_url?: SortOrder
    created_at?: SortOrder
    updated_at?: SortOrder
  }

  export type medicationsMaxOrderByAggregateInput = {
    id?: SortOrder
    user_id?: SortOrder
    name?: SortOrder
    instructions?: SortOrder
    start_date?: SortOrder
    end_date?: SortOrder
    active?: SortOrder
    medication_type?: SortOrder
    image_url?: SortOrder
    created_at?: SortOrder
    updated_at?: SortOrder
  }

  export type medicationsMinOrderByAggregateInput = {
    id?: SortOrder
    user_id?: SortOrder
    name?: SortOrder
    instructions?: SortOrder
    start_date?: SortOrder
    end_date?: SortOrder
    active?: SortOrder
    medication_type?: SortOrder
    image_url?: SortOrder
    created_at?: SortOrder
    updated_at?: SortOrder
  }
  export type JsonWithAggregatesFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonWithAggregatesFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonWithAggregatesFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonWithAggregatesFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonWithAggregatesFilterBase<$PrismaModel>>, 'path'>>

  export type JsonWithAggregatesFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedJsonFilter<$PrismaModel>
    _max?: NestedJsonFilter<$PrismaModel>
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type MedicationsScalarRelationFilter = {
    is?: medicationsWhereInput
    isNot?: medicationsWhereInput
  }

  export type adherenceCountOrderByAggregateInput = {
    id?: SortOrder
    user_id?: SortOrder
    medication_id?: SortOrder
    scheduled_time?: SortOrder
    scheduled_date?: SortOrder
    taken_time?: SortOrder
    status?: SortOrder
    notes?: SortOrder
    reminder_sent?: SortOrder
    side_effects_reported?: SortOrder
    dosage_taken?: SortOrder
    created_at?: SortOrder
    updated_at?: SortOrder
  }

  export type adherenceMaxOrderByAggregateInput = {
    id?: SortOrder
    user_id?: SortOrder
    medication_id?: SortOrder
    scheduled_time?: SortOrder
    scheduled_date?: SortOrder
    taken_time?: SortOrder
    status?: SortOrder
    notes?: SortOrder
    reminder_sent?: SortOrder
    created_at?: SortOrder
    updated_at?: SortOrder
  }

  export type adherenceMinOrderByAggregateInput = {
    id?: SortOrder
    user_id?: SortOrder
    medication_id?: SortOrder
    scheduled_time?: SortOrder
    scheduled_date?: SortOrder
    taken_time?: SortOrder
    status?: SortOrder
    notes?: SortOrder
    reminder_sent?: SortOrder
    created_at?: SortOrder
    updated_at?: SortOrder
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type IntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type UuidNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedUuidNullableFilter<$PrismaModel> | string | null
  }

  export type AdherenceNullableScalarRelationFilter = {
    is?: adherenceWhereInput | null
    isNot?: adherenceWhereInput | null
  }

  export type remindersCountOrderByAggregateInput = {
    id?: SortOrder
    user_id?: SortOrder
    medication_id?: SortOrder
    scheduled_time?: SortOrder
    scheduled_date?: SortOrder
    status?: SortOrder
    channels?: SortOrder
    message?: SortOrder
    retry_count?: SortOrder
    last_retry?: SortOrder
    adherence_id?: SortOrder
    created_at?: SortOrder
    updated_at?: SortOrder
  }

  export type remindersAvgOrderByAggregateInput = {
    retry_count?: SortOrder
  }

  export type remindersMaxOrderByAggregateInput = {
    id?: SortOrder
    user_id?: SortOrder
    medication_id?: SortOrder
    scheduled_time?: SortOrder
    scheduled_date?: SortOrder
    status?: SortOrder
    message?: SortOrder
    retry_count?: SortOrder
    last_retry?: SortOrder
    adherence_id?: SortOrder
    created_at?: SortOrder
    updated_at?: SortOrder
  }

  export type remindersMinOrderByAggregateInput = {
    id?: SortOrder
    user_id?: SortOrder
    medication_id?: SortOrder
    scheduled_time?: SortOrder
    scheduled_date?: SortOrder
    status?: SortOrder
    message?: SortOrder
    retry_count?: SortOrder
    last_retry?: SortOrder
    adherence_id?: SortOrder
    created_at?: SortOrder
    updated_at?: SortOrder
  }

  export type remindersSumOrderByAggregateInput = {
    retry_count?: SortOrder
  }

  export type IntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type UuidNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedUuidNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type usersCreateallergiesInput = {
    set: string[]
  }

  export type usersCreateconditionsInput = {
    set: string[]
  }

  export type adherenceCreateNestedManyWithoutUserInput = {
    create?: XOR<adherenceCreateWithoutUserInput, adherenceUncheckedCreateWithoutUserInput> | adherenceCreateWithoutUserInput[] | adherenceUncheckedCreateWithoutUserInput[]
    connectOrCreate?: adherenceCreateOrConnectWithoutUserInput | adherenceCreateOrConnectWithoutUserInput[]
    createMany?: adherenceCreateManyUserInputEnvelope
    connect?: adherenceWhereUniqueInput | adherenceWhereUniqueInput[]
  }

  export type medicationsCreateNestedManyWithoutUserInput = {
    create?: XOR<medicationsCreateWithoutUserInput, medicationsUncheckedCreateWithoutUserInput> | medicationsCreateWithoutUserInput[] | medicationsUncheckedCreateWithoutUserInput[]
    connectOrCreate?: medicationsCreateOrConnectWithoutUserInput | medicationsCreateOrConnectWithoutUserInput[]
    createMany?: medicationsCreateManyUserInputEnvelope
    connect?: medicationsWhereUniqueInput | medicationsWhereUniqueInput[]
  }

  export type remindersCreateNestedManyWithoutUserInput = {
    create?: XOR<remindersCreateWithoutUserInput, remindersUncheckedCreateWithoutUserInput> | remindersCreateWithoutUserInput[] | remindersUncheckedCreateWithoutUserInput[]
    connectOrCreate?: remindersCreateOrConnectWithoutUserInput | remindersCreateOrConnectWithoutUserInput[]
    createMany?: remindersCreateManyUserInputEnvelope
    connect?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
  }

  export type user_settingsCreateNestedOneWithoutUserInput = {
    create?: XOR<user_settingsCreateWithoutUserInput, user_settingsUncheckedCreateWithoutUserInput>
    connectOrCreate?: user_settingsCreateOrConnectWithoutUserInput
    connect?: user_settingsWhereUniqueInput
  }

  export type adherenceUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<adherenceCreateWithoutUserInput, adherenceUncheckedCreateWithoutUserInput> | adherenceCreateWithoutUserInput[] | adherenceUncheckedCreateWithoutUserInput[]
    connectOrCreate?: adherenceCreateOrConnectWithoutUserInput | adherenceCreateOrConnectWithoutUserInput[]
    createMany?: adherenceCreateManyUserInputEnvelope
    connect?: adherenceWhereUniqueInput | adherenceWhereUniqueInput[]
  }

  export type medicationsUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<medicationsCreateWithoutUserInput, medicationsUncheckedCreateWithoutUserInput> | medicationsCreateWithoutUserInput[] | medicationsUncheckedCreateWithoutUserInput[]
    connectOrCreate?: medicationsCreateOrConnectWithoutUserInput | medicationsCreateOrConnectWithoutUserInput[]
    createMany?: medicationsCreateManyUserInputEnvelope
    connect?: medicationsWhereUniqueInput | medicationsWhereUniqueInput[]
  }

  export type remindersUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<remindersCreateWithoutUserInput, remindersUncheckedCreateWithoutUserInput> | remindersCreateWithoutUserInput[] | remindersUncheckedCreateWithoutUserInput[]
    connectOrCreate?: remindersCreateOrConnectWithoutUserInput | remindersCreateOrConnectWithoutUserInput[]
    createMany?: remindersCreateManyUserInputEnvelope
    connect?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
  }

  export type user_settingsUncheckedCreateNestedOneWithoutUserInput = {
    create?: XOR<user_settingsCreateWithoutUserInput, user_settingsUncheckedCreateWithoutUserInput>
    connectOrCreate?: user_settingsCreateOrConnectWithoutUserInput
    connect?: user_settingsWhereUniqueInput
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type NullableDateTimeFieldUpdateOperationsInput = {
    set?: Date | string | null
  }

  export type usersUpdateallergiesInput = {
    set?: string[]
    push?: string | string[]
  }

  export type usersUpdateconditionsInput = {
    set?: string[]
    push?: string | string[]
  }

  export type NullableBoolFieldUpdateOperationsInput = {
    set?: boolean | null
  }

  export type adherenceUpdateManyWithoutUserNestedInput = {
    create?: XOR<adherenceCreateWithoutUserInput, adherenceUncheckedCreateWithoutUserInput> | adherenceCreateWithoutUserInput[] | adherenceUncheckedCreateWithoutUserInput[]
    connectOrCreate?: adherenceCreateOrConnectWithoutUserInput | adherenceCreateOrConnectWithoutUserInput[]
    upsert?: adherenceUpsertWithWhereUniqueWithoutUserInput | adherenceUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: adherenceCreateManyUserInputEnvelope
    set?: adherenceWhereUniqueInput | adherenceWhereUniqueInput[]
    disconnect?: adherenceWhereUniqueInput | adherenceWhereUniqueInput[]
    delete?: adherenceWhereUniqueInput | adherenceWhereUniqueInput[]
    connect?: adherenceWhereUniqueInput | adherenceWhereUniqueInput[]
    update?: adherenceUpdateWithWhereUniqueWithoutUserInput | adherenceUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: adherenceUpdateManyWithWhereWithoutUserInput | adherenceUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: adherenceScalarWhereInput | adherenceScalarWhereInput[]
  }

  export type medicationsUpdateManyWithoutUserNestedInput = {
    create?: XOR<medicationsCreateWithoutUserInput, medicationsUncheckedCreateWithoutUserInput> | medicationsCreateWithoutUserInput[] | medicationsUncheckedCreateWithoutUserInput[]
    connectOrCreate?: medicationsCreateOrConnectWithoutUserInput | medicationsCreateOrConnectWithoutUserInput[]
    upsert?: medicationsUpsertWithWhereUniqueWithoutUserInput | medicationsUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: medicationsCreateManyUserInputEnvelope
    set?: medicationsWhereUniqueInput | medicationsWhereUniqueInput[]
    disconnect?: medicationsWhereUniqueInput | medicationsWhereUniqueInput[]
    delete?: medicationsWhereUniqueInput | medicationsWhereUniqueInput[]
    connect?: medicationsWhereUniqueInput | medicationsWhereUniqueInput[]
    update?: medicationsUpdateWithWhereUniqueWithoutUserInput | medicationsUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: medicationsUpdateManyWithWhereWithoutUserInput | medicationsUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: medicationsScalarWhereInput | medicationsScalarWhereInput[]
  }

  export type remindersUpdateManyWithoutUserNestedInput = {
    create?: XOR<remindersCreateWithoutUserInput, remindersUncheckedCreateWithoutUserInput> | remindersCreateWithoutUserInput[] | remindersUncheckedCreateWithoutUserInput[]
    connectOrCreate?: remindersCreateOrConnectWithoutUserInput | remindersCreateOrConnectWithoutUserInput[]
    upsert?: remindersUpsertWithWhereUniqueWithoutUserInput | remindersUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: remindersCreateManyUserInputEnvelope
    set?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    disconnect?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    delete?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    connect?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    update?: remindersUpdateWithWhereUniqueWithoutUserInput | remindersUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: remindersUpdateManyWithWhereWithoutUserInput | remindersUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: remindersScalarWhereInput | remindersScalarWhereInput[]
  }

  export type user_settingsUpdateOneWithoutUserNestedInput = {
    create?: XOR<user_settingsCreateWithoutUserInput, user_settingsUncheckedCreateWithoutUserInput>
    connectOrCreate?: user_settingsCreateOrConnectWithoutUserInput
    upsert?: user_settingsUpsertWithoutUserInput
    disconnect?: user_settingsWhereInput | boolean
    delete?: user_settingsWhereInput | boolean
    connect?: user_settingsWhereUniqueInput
    update?: XOR<XOR<user_settingsUpdateToOneWithWhereWithoutUserInput, user_settingsUpdateWithoutUserInput>, user_settingsUncheckedUpdateWithoutUserInput>
  }

  export type adherenceUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<adherenceCreateWithoutUserInput, adherenceUncheckedCreateWithoutUserInput> | adherenceCreateWithoutUserInput[] | adherenceUncheckedCreateWithoutUserInput[]
    connectOrCreate?: adherenceCreateOrConnectWithoutUserInput | adherenceCreateOrConnectWithoutUserInput[]
    upsert?: adherenceUpsertWithWhereUniqueWithoutUserInput | adherenceUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: adherenceCreateManyUserInputEnvelope
    set?: adherenceWhereUniqueInput | adherenceWhereUniqueInput[]
    disconnect?: adherenceWhereUniqueInput | adherenceWhereUniqueInput[]
    delete?: adherenceWhereUniqueInput | adherenceWhereUniqueInput[]
    connect?: adherenceWhereUniqueInput | adherenceWhereUniqueInput[]
    update?: adherenceUpdateWithWhereUniqueWithoutUserInput | adherenceUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: adherenceUpdateManyWithWhereWithoutUserInput | adherenceUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: adherenceScalarWhereInput | adherenceScalarWhereInput[]
  }

  export type medicationsUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<medicationsCreateWithoutUserInput, medicationsUncheckedCreateWithoutUserInput> | medicationsCreateWithoutUserInput[] | medicationsUncheckedCreateWithoutUserInput[]
    connectOrCreate?: medicationsCreateOrConnectWithoutUserInput | medicationsCreateOrConnectWithoutUserInput[]
    upsert?: medicationsUpsertWithWhereUniqueWithoutUserInput | medicationsUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: medicationsCreateManyUserInputEnvelope
    set?: medicationsWhereUniqueInput | medicationsWhereUniqueInput[]
    disconnect?: medicationsWhereUniqueInput | medicationsWhereUniqueInput[]
    delete?: medicationsWhereUniqueInput | medicationsWhereUniqueInput[]
    connect?: medicationsWhereUniqueInput | medicationsWhereUniqueInput[]
    update?: medicationsUpdateWithWhereUniqueWithoutUserInput | medicationsUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: medicationsUpdateManyWithWhereWithoutUserInput | medicationsUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: medicationsScalarWhereInput | medicationsScalarWhereInput[]
  }

  export type remindersUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<remindersCreateWithoutUserInput, remindersUncheckedCreateWithoutUserInput> | remindersCreateWithoutUserInput[] | remindersUncheckedCreateWithoutUserInput[]
    connectOrCreate?: remindersCreateOrConnectWithoutUserInput | remindersCreateOrConnectWithoutUserInput[]
    upsert?: remindersUpsertWithWhereUniqueWithoutUserInput | remindersUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: remindersCreateManyUserInputEnvelope
    set?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    disconnect?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    delete?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    connect?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    update?: remindersUpdateWithWhereUniqueWithoutUserInput | remindersUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: remindersUpdateManyWithWhereWithoutUserInput | remindersUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: remindersScalarWhereInput | remindersScalarWhereInput[]
  }

  export type user_settingsUncheckedUpdateOneWithoutUserNestedInput = {
    create?: XOR<user_settingsCreateWithoutUserInput, user_settingsUncheckedCreateWithoutUserInput>
    connectOrCreate?: user_settingsCreateOrConnectWithoutUserInput
    upsert?: user_settingsUpsertWithoutUserInput
    disconnect?: user_settingsWhereInput | boolean
    delete?: user_settingsWhereInput | boolean
    connect?: user_settingsWhereUniqueInput
    update?: XOR<XOR<user_settingsUpdateToOneWithWhereWithoutUserInput, user_settingsUpdateWithoutUserInput>, user_settingsUncheckedUpdateWithoutUserInput>
  }

  export type user_settingsCreatepreferred_timesInput = {
    set: string[]
  }

  export type usersCreateNestedOneWithoutSettingsInput = {
    create?: XOR<usersCreateWithoutSettingsInput, usersUncheckedCreateWithoutSettingsInput>
    connectOrCreate?: usersCreateOrConnectWithoutSettingsInput
    connect?: usersWhereUniqueInput
  }

  export type BoolFieldUpdateOperationsInput = {
    set?: boolean
  }

  export type user_settingsUpdatepreferred_timesInput = {
    set?: string[]
    push?: string | string[]
  }

  export type usersUpdateOneRequiredWithoutSettingsNestedInput = {
    create?: XOR<usersCreateWithoutSettingsInput, usersUncheckedCreateWithoutSettingsInput>
    connectOrCreate?: usersCreateOrConnectWithoutSettingsInput
    upsert?: usersUpsertWithoutSettingsInput
    connect?: usersWhereUniqueInput
    update?: XOR<XOR<usersUpdateToOneWithWhereWithoutSettingsInput, usersUpdateWithoutSettingsInput>, usersUncheckedUpdateWithoutSettingsInput>
  }

  export type medicationsCreatescheduled_timesInput = {
    set: string[]
  }

  export type medicationsCreateside_effects_to_watchInput = {
    set: string[]
  }

  export type adherenceCreateNestedManyWithoutMedicationInput = {
    create?: XOR<adherenceCreateWithoutMedicationInput, adherenceUncheckedCreateWithoutMedicationInput> | adherenceCreateWithoutMedicationInput[] | adherenceUncheckedCreateWithoutMedicationInput[]
    connectOrCreate?: adherenceCreateOrConnectWithoutMedicationInput | adherenceCreateOrConnectWithoutMedicationInput[]
    createMany?: adherenceCreateManyMedicationInputEnvelope
    connect?: adherenceWhereUniqueInput | adherenceWhereUniqueInput[]
  }

  export type usersCreateNestedOneWithoutMedicationInput = {
    create?: XOR<usersCreateWithoutMedicationInput, usersUncheckedCreateWithoutMedicationInput>
    connectOrCreate?: usersCreateOrConnectWithoutMedicationInput
    connect?: usersWhereUniqueInput
  }

  export type remindersCreateNestedManyWithoutMedicationInput = {
    create?: XOR<remindersCreateWithoutMedicationInput, remindersUncheckedCreateWithoutMedicationInput> | remindersCreateWithoutMedicationInput[] | remindersUncheckedCreateWithoutMedicationInput[]
    connectOrCreate?: remindersCreateOrConnectWithoutMedicationInput | remindersCreateOrConnectWithoutMedicationInput[]
    createMany?: remindersCreateManyMedicationInputEnvelope
    connect?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
  }

  export type adherenceUncheckedCreateNestedManyWithoutMedicationInput = {
    create?: XOR<adherenceCreateWithoutMedicationInput, adherenceUncheckedCreateWithoutMedicationInput> | adherenceCreateWithoutMedicationInput[] | adherenceUncheckedCreateWithoutMedicationInput[]
    connectOrCreate?: adherenceCreateOrConnectWithoutMedicationInput | adherenceCreateOrConnectWithoutMedicationInput[]
    createMany?: adherenceCreateManyMedicationInputEnvelope
    connect?: adherenceWhereUniqueInput | adherenceWhereUniqueInput[]
  }

  export type remindersUncheckedCreateNestedManyWithoutMedicationInput = {
    create?: XOR<remindersCreateWithoutMedicationInput, remindersUncheckedCreateWithoutMedicationInput> | remindersCreateWithoutMedicationInput[] | remindersUncheckedCreateWithoutMedicationInput[]
    connectOrCreate?: remindersCreateOrConnectWithoutMedicationInput | remindersCreateOrConnectWithoutMedicationInput[]
    createMany?: remindersCreateManyMedicationInputEnvelope
    connect?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
  }

  export type medicationsUpdatescheduled_timesInput = {
    set?: string[]
    push?: string | string[]
  }

  export type medicationsUpdateside_effects_to_watchInput = {
    set?: string[]
    push?: string | string[]
  }

  export type adherenceUpdateManyWithoutMedicationNestedInput = {
    create?: XOR<adherenceCreateWithoutMedicationInput, adherenceUncheckedCreateWithoutMedicationInput> | adherenceCreateWithoutMedicationInput[] | adherenceUncheckedCreateWithoutMedicationInput[]
    connectOrCreate?: adherenceCreateOrConnectWithoutMedicationInput | adherenceCreateOrConnectWithoutMedicationInput[]
    upsert?: adherenceUpsertWithWhereUniqueWithoutMedicationInput | adherenceUpsertWithWhereUniqueWithoutMedicationInput[]
    createMany?: adherenceCreateManyMedicationInputEnvelope
    set?: adherenceWhereUniqueInput | adherenceWhereUniqueInput[]
    disconnect?: adherenceWhereUniqueInput | adherenceWhereUniqueInput[]
    delete?: adherenceWhereUniqueInput | adherenceWhereUniqueInput[]
    connect?: adherenceWhereUniqueInput | adherenceWhereUniqueInput[]
    update?: adherenceUpdateWithWhereUniqueWithoutMedicationInput | adherenceUpdateWithWhereUniqueWithoutMedicationInput[]
    updateMany?: adherenceUpdateManyWithWhereWithoutMedicationInput | adherenceUpdateManyWithWhereWithoutMedicationInput[]
    deleteMany?: adherenceScalarWhereInput | adherenceScalarWhereInput[]
  }

  export type usersUpdateOneRequiredWithoutMedicationNestedInput = {
    create?: XOR<usersCreateWithoutMedicationInput, usersUncheckedCreateWithoutMedicationInput>
    connectOrCreate?: usersCreateOrConnectWithoutMedicationInput
    upsert?: usersUpsertWithoutMedicationInput
    connect?: usersWhereUniqueInput
    update?: XOR<XOR<usersUpdateToOneWithWhereWithoutMedicationInput, usersUpdateWithoutMedicationInput>, usersUncheckedUpdateWithoutMedicationInput>
  }

  export type remindersUpdateManyWithoutMedicationNestedInput = {
    create?: XOR<remindersCreateWithoutMedicationInput, remindersUncheckedCreateWithoutMedicationInput> | remindersCreateWithoutMedicationInput[] | remindersUncheckedCreateWithoutMedicationInput[]
    connectOrCreate?: remindersCreateOrConnectWithoutMedicationInput | remindersCreateOrConnectWithoutMedicationInput[]
    upsert?: remindersUpsertWithWhereUniqueWithoutMedicationInput | remindersUpsertWithWhereUniqueWithoutMedicationInput[]
    createMany?: remindersCreateManyMedicationInputEnvelope
    set?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    disconnect?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    delete?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    connect?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    update?: remindersUpdateWithWhereUniqueWithoutMedicationInput | remindersUpdateWithWhereUniqueWithoutMedicationInput[]
    updateMany?: remindersUpdateManyWithWhereWithoutMedicationInput | remindersUpdateManyWithWhereWithoutMedicationInput[]
    deleteMany?: remindersScalarWhereInput | remindersScalarWhereInput[]
  }

  export type adherenceUncheckedUpdateManyWithoutMedicationNestedInput = {
    create?: XOR<adherenceCreateWithoutMedicationInput, adherenceUncheckedCreateWithoutMedicationInput> | adherenceCreateWithoutMedicationInput[] | adherenceUncheckedCreateWithoutMedicationInput[]
    connectOrCreate?: adherenceCreateOrConnectWithoutMedicationInput | adherenceCreateOrConnectWithoutMedicationInput[]
    upsert?: adherenceUpsertWithWhereUniqueWithoutMedicationInput | adherenceUpsertWithWhereUniqueWithoutMedicationInput[]
    createMany?: adherenceCreateManyMedicationInputEnvelope
    set?: adherenceWhereUniqueInput | adherenceWhereUniqueInput[]
    disconnect?: adherenceWhereUniqueInput | adherenceWhereUniqueInput[]
    delete?: adherenceWhereUniqueInput | adherenceWhereUniqueInput[]
    connect?: adherenceWhereUniqueInput | adherenceWhereUniqueInput[]
    update?: adherenceUpdateWithWhereUniqueWithoutMedicationInput | adherenceUpdateWithWhereUniqueWithoutMedicationInput[]
    updateMany?: adherenceUpdateManyWithWhereWithoutMedicationInput | adherenceUpdateManyWithWhereWithoutMedicationInput[]
    deleteMany?: adherenceScalarWhereInput | adherenceScalarWhereInput[]
  }

  export type remindersUncheckedUpdateManyWithoutMedicationNestedInput = {
    create?: XOR<remindersCreateWithoutMedicationInput, remindersUncheckedCreateWithoutMedicationInput> | remindersCreateWithoutMedicationInput[] | remindersUncheckedCreateWithoutMedicationInput[]
    connectOrCreate?: remindersCreateOrConnectWithoutMedicationInput | remindersCreateOrConnectWithoutMedicationInput[]
    upsert?: remindersUpsertWithWhereUniqueWithoutMedicationInput | remindersUpsertWithWhereUniqueWithoutMedicationInput[]
    createMany?: remindersCreateManyMedicationInputEnvelope
    set?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    disconnect?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    delete?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    connect?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    update?: remindersUpdateWithWhereUniqueWithoutMedicationInput | remindersUpdateWithWhereUniqueWithoutMedicationInput[]
    updateMany?: remindersUpdateManyWithWhereWithoutMedicationInput | remindersUpdateManyWithWhereWithoutMedicationInput[]
    deleteMany?: remindersScalarWhereInput | remindersScalarWhereInput[]
  }

  export type adherenceCreateside_effects_reportedInput = {
    set: string[]
  }

  export type medicationsCreateNestedOneWithoutAdherenceInput = {
    create?: XOR<medicationsCreateWithoutAdherenceInput, medicationsUncheckedCreateWithoutAdherenceInput>
    connectOrCreate?: medicationsCreateOrConnectWithoutAdherenceInput
    connect?: medicationsWhereUniqueInput
  }

  export type usersCreateNestedOneWithoutAdherenceInput = {
    create?: XOR<usersCreateWithoutAdherenceInput, usersUncheckedCreateWithoutAdherenceInput>
    connectOrCreate?: usersCreateOrConnectWithoutAdherenceInput
    connect?: usersWhereUniqueInput
  }

  export type remindersCreateNestedManyWithoutAdherenceInput = {
    create?: XOR<remindersCreateWithoutAdherenceInput, remindersUncheckedCreateWithoutAdherenceInput> | remindersCreateWithoutAdherenceInput[] | remindersUncheckedCreateWithoutAdherenceInput[]
    connectOrCreate?: remindersCreateOrConnectWithoutAdherenceInput | remindersCreateOrConnectWithoutAdherenceInput[]
    createMany?: remindersCreateManyAdherenceInputEnvelope
    connect?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
  }

  export type remindersUncheckedCreateNestedManyWithoutAdherenceInput = {
    create?: XOR<remindersCreateWithoutAdherenceInput, remindersUncheckedCreateWithoutAdherenceInput> | remindersCreateWithoutAdherenceInput[] | remindersUncheckedCreateWithoutAdherenceInput[]
    connectOrCreate?: remindersCreateOrConnectWithoutAdherenceInput | remindersCreateOrConnectWithoutAdherenceInput[]
    createMany?: remindersCreateManyAdherenceInputEnvelope
    connect?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type adherenceUpdateside_effects_reportedInput = {
    set?: string[]
    push?: string | string[]
  }

  export type medicationsUpdateOneRequiredWithoutAdherenceNestedInput = {
    create?: XOR<medicationsCreateWithoutAdherenceInput, medicationsUncheckedCreateWithoutAdherenceInput>
    connectOrCreate?: medicationsCreateOrConnectWithoutAdherenceInput
    upsert?: medicationsUpsertWithoutAdherenceInput
    connect?: medicationsWhereUniqueInput
    update?: XOR<XOR<medicationsUpdateToOneWithWhereWithoutAdherenceInput, medicationsUpdateWithoutAdherenceInput>, medicationsUncheckedUpdateWithoutAdherenceInput>
  }

  export type usersUpdateOneRequiredWithoutAdherenceNestedInput = {
    create?: XOR<usersCreateWithoutAdherenceInput, usersUncheckedCreateWithoutAdherenceInput>
    connectOrCreate?: usersCreateOrConnectWithoutAdherenceInput
    upsert?: usersUpsertWithoutAdherenceInput
    connect?: usersWhereUniqueInput
    update?: XOR<XOR<usersUpdateToOneWithWhereWithoutAdherenceInput, usersUpdateWithoutAdherenceInput>, usersUncheckedUpdateWithoutAdherenceInput>
  }

  export type remindersUpdateManyWithoutAdherenceNestedInput = {
    create?: XOR<remindersCreateWithoutAdherenceInput, remindersUncheckedCreateWithoutAdherenceInput> | remindersCreateWithoutAdherenceInput[] | remindersUncheckedCreateWithoutAdherenceInput[]
    connectOrCreate?: remindersCreateOrConnectWithoutAdherenceInput | remindersCreateOrConnectWithoutAdherenceInput[]
    upsert?: remindersUpsertWithWhereUniqueWithoutAdherenceInput | remindersUpsertWithWhereUniqueWithoutAdherenceInput[]
    createMany?: remindersCreateManyAdherenceInputEnvelope
    set?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    disconnect?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    delete?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    connect?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    update?: remindersUpdateWithWhereUniqueWithoutAdherenceInput | remindersUpdateWithWhereUniqueWithoutAdherenceInput[]
    updateMany?: remindersUpdateManyWithWhereWithoutAdherenceInput | remindersUpdateManyWithWhereWithoutAdherenceInput[]
    deleteMany?: remindersScalarWhereInput | remindersScalarWhereInput[]
  }

  export type remindersUncheckedUpdateManyWithoutAdherenceNestedInput = {
    create?: XOR<remindersCreateWithoutAdherenceInput, remindersUncheckedCreateWithoutAdherenceInput> | remindersCreateWithoutAdherenceInput[] | remindersUncheckedCreateWithoutAdherenceInput[]
    connectOrCreate?: remindersCreateOrConnectWithoutAdherenceInput | remindersCreateOrConnectWithoutAdherenceInput[]
    upsert?: remindersUpsertWithWhereUniqueWithoutAdherenceInput | remindersUpsertWithWhereUniqueWithoutAdherenceInput[]
    createMany?: remindersCreateManyAdherenceInputEnvelope
    set?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    disconnect?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    delete?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    connect?: remindersWhereUniqueInput | remindersWhereUniqueInput[]
    update?: remindersUpdateWithWhereUniqueWithoutAdherenceInput | remindersUpdateWithWhereUniqueWithoutAdherenceInput[]
    updateMany?: remindersUpdateManyWithWhereWithoutAdherenceInput | remindersUpdateManyWithWhereWithoutAdherenceInput[]
    deleteMany?: remindersScalarWhereInput | remindersScalarWhereInput[]
  }

  export type adherenceCreateNestedOneWithoutRemindersInput = {
    create?: XOR<adherenceCreateWithoutRemindersInput, adherenceUncheckedCreateWithoutRemindersInput>
    connectOrCreate?: adherenceCreateOrConnectWithoutRemindersInput
    connect?: adherenceWhereUniqueInput
  }

  export type medicationsCreateNestedOneWithoutRemindersInput = {
    create?: XOR<medicationsCreateWithoutRemindersInput, medicationsUncheckedCreateWithoutRemindersInput>
    connectOrCreate?: medicationsCreateOrConnectWithoutRemindersInput
    connect?: medicationsWhereUniqueInput
  }

  export type usersCreateNestedOneWithoutRemindersInput = {
    create?: XOR<usersCreateWithoutRemindersInput, usersUncheckedCreateWithoutRemindersInput>
    connectOrCreate?: usersCreateOrConnectWithoutRemindersInput
    connect?: usersWhereUniqueInput
  }

  export type NullableIntFieldUpdateOperationsInput = {
    set?: number | null
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type adherenceUpdateOneWithoutRemindersNestedInput = {
    create?: XOR<adherenceCreateWithoutRemindersInput, adherenceUncheckedCreateWithoutRemindersInput>
    connectOrCreate?: adherenceCreateOrConnectWithoutRemindersInput
    upsert?: adherenceUpsertWithoutRemindersInput
    disconnect?: adherenceWhereInput | boolean
    delete?: adherenceWhereInput | boolean
    connect?: adherenceWhereUniqueInput
    update?: XOR<XOR<adherenceUpdateToOneWithWhereWithoutRemindersInput, adherenceUpdateWithoutRemindersInput>, adherenceUncheckedUpdateWithoutRemindersInput>
  }

  export type medicationsUpdateOneRequiredWithoutRemindersNestedInput = {
    create?: XOR<medicationsCreateWithoutRemindersInput, medicationsUncheckedCreateWithoutRemindersInput>
    connectOrCreate?: medicationsCreateOrConnectWithoutRemindersInput
    upsert?: medicationsUpsertWithoutRemindersInput
    connect?: medicationsWhereUniqueInput
    update?: XOR<XOR<medicationsUpdateToOneWithWhereWithoutRemindersInput, medicationsUpdateWithoutRemindersInput>, medicationsUncheckedUpdateWithoutRemindersInput>
  }

  export type usersUpdateOneRequiredWithoutRemindersNestedInput = {
    create?: XOR<usersCreateWithoutRemindersInput, usersUncheckedCreateWithoutRemindersInput>
    connectOrCreate?: usersCreateOrConnectWithoutRemindersInput
    upsert?: usersUpsertWithoutRemindersInput
    connect?: usersWhereUniqueInput
    update?: XOR<XOR<usersUpdateToOneWithWhereWithoutRemindersInput, usersUpdateWithoutRemindersInput>, usersUncheckedUpdateWithoutRemindersInput>
  }

  export type NestedUuidFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedUuidFilter<$PrismaModel> | string
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedDateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type NestedBoolNullableFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel> | null
    not?: NestedBoolNullableFilter<$PrismaModel> | boolean | null
  }

  export type NestedUuidWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedUuidWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type NestedDateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type NestedBoolNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel> | null
    not?: NestedBoolNullableWithAggregatesFilter<$PrismaModel> | boolean | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedBoolNullableFilter<$PrismaModel>
    _max?: NestedBoolNullableFilter<$PrismaModel>
  }
  export type NestedJsonNullableFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<NestedJsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>,
        Required<NestedJsonNullableFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>

  export type NestedJsonNullableFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type NestedBoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type NestedBoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }
  export type NestedJsonFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<NestedJsonFilterBase<$PrismaModel>>, Exclude<keyof Required<NestedJsonFilterBase<$PrismaModel>>, 'path'>>,
        Required<NestedJsonFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<NestedJsonFilterBase<$PrismaModel>>, 'path'>>

  export type NestedJsonFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type NestedUuidNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedUuidNullableFilter<$PrismaModel> | string | null
  }

  export type NestedIntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type NestedFloatNullableFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableFilter<$PrismaModel> | number | null
  }

  export type NestedUuidNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedUuidNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type adherenceCreateWithoutUserInput = {
    id?: string
    scheduled_time: string
    scheduled_date: Date | string
    taken_time?: Date | string | null
    status?: string | null
    notes?: string | null
    reminder_sent?: boolean | null
    side_effects_reported?: adherenceCreateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
    medication: medicationsCreateNestedOneWithoutAdherenceInput
    reminders?: remindersCreateNestedManyWithoutAdherenceInput
  }

  export type adherenceUncheckedCreateWithoutUserInput = {
    id?: string
    medication_id: string
    scheduled_time: string
    scheduled_date: Date | string
    taken_time?: Date | string | null
    status?: string | null
    notes?: string | null
    reminder_sent?: boolean | null
    side_effects_reported?: adherenceCreateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
    reminders?: remindersUncheckedCreateNestedManyWithoutAdherenceInput
  }

  export type adherenceCreateOrConnectWithoutUserInput = {
    where: adherenceWhereUniqueInput
    create: XOR<adherenceCreateWithoutUserInput, adherenceUncheckedCreateWithoutUserInput>
  }

  export type adherenceCreateManyUserInputEnvelope = {
    data: adherenceCreateManyUserInput | adherenceCreateManyUserInput[]
    skipDuplicates?: boolean
  }

  export type medicationsCreateWithoutUserInput = {
    id?: string
    name: string
    dosage: JsonNullValueInput | InputJsonValue
    frequency: JsonNullValueInput | InputJsonValue
    scheduled_times?: medicationsCreatescheduled_timesInput | string[]
    instructions?: string | null
    start_date?: Date | string | null
    end_date?: Date | string | null
    refill_reminder?: NullableJsonNullValueInput | InputJsonValue
    side_effects_to_watch?: medicationsCreateside_effects_to_watchInput | string[]
    active?: boolean | null
    medication_type?: string | null
    image_url?: string | null
    created_at?: Date | string | null
    updated_at?: Date | string | null
    adherence?: adherenceCreateNestedManyWithoutMedicationInput
    reminders?: remindersCreateNestedManyWithoutMedicationInput
  }

  export type medicationsUncheckedCreateWithoutUserInput = {
    id?: string
    name: string
    dosage: JsonNullValueInput | InputJsonValue
    frequency: JsonNullValueInput | InputJsonValue
    scheduled_times?: medicationsCreatescheduled_timesInput | string[]
    instructions?: string | null
    start_date?: Date | string | null
    end_date?: Date | string | null
    refill_reminder?: NullableJsonNullValueInput | InputJsonValue
    side_effects_to_watch?: medicationsCreateside_effects_to_watchInput | string[]
    active?: boolean | null
    medication_type?: string | null
    image_url?: string | null
    created_at?: Date | string | null
    updated_at?: Date | string | null
    adherence?: adherenceUncheckedCreateNestedManyWithoutMedicationInput
    reminders?: remindersUncheckedCreateNestedManyWithoutMedicationInput
  }

  export type medicationsCreateOrConnectWithoutUserInput = {
    where: medicationsWhereUniqueInput
    create: XOR<medicationsCreateWithoutUserInput, medicationsUncheckedCreateWithoutUserInput>
  }

  export type medicationsCreateManyUserInputEnvelope = {
    data: medicationsCreateManyUserInput | medicationsCreateManyUserInput[]
    skipDuplicates?: boolean
  }

  export type remindersCreateWithoutUserInput = {
    id?: string
    scheduled_time: string
    scheduled_date: Date | string
    status?: string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: string | null
    retry_count?: number | null
    last_retry?: Date | string | null
    created_at?: Date | string | null
    updated_at?: Date | string | null
    adherence?: adherenceCreateNestedOneWithoutRemindersInput
    medication: medicationsCreateNestedOneWithoutRemindersInput
  }

  export type remindersUncheckedCreateWithoutUserInput = {
    id?: string
    medication_id: string
    scheduled_time: string
    scheduled_date: Date | string
    status?: string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: string | null
    retry_count?: number | null
    last_retry?: Date | string | null
    adherence_id?: string | null
    created_at?: Date | string | null
    updated_at?: Date | string | null
  }

  export type remindersCreateOrConnectWithoutUserInput = {
    where: remindersWhereUniqueInput
    create: XOR<remindersCreateWithoutUserInput, remindersUncheckedCreateWithoutUserInput>
  }

  export type remindersCreateManyUserInputEnvelope = {
    data: remindersCreateManyUserInput | remindersCreateManyUserInput[]
    skipDuplicates?: boolean
  }

  export type user_settingsCreateWithoutUserInput = {
    id?: string
    email_enabled: boolean
    preferred_times?: user_settingsCreatepreferred_timesInput | string[]
    timezone: string
    notification_preferences?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
  }

  export type user_settingsUncheckedCreateWithoutUserInput = {
    id?: string
    email_enabled: boolean
    preferred_times?: user_settingsCreatepreferred_timesInput | string[]
    timezone: string
    notification_preferences?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
  }

  export type user_settingsCreateOrConnectWithoutUserInput = {
    where: user_settingsWhereUniqueInput
    create: XOR<user_settingsCreateWithoutUserInput, user_settingsUncheckedCreateWithoutUserInput>
  }

  export type adherenceUpsertWithWhereUniqueWithoutUserInput = {
    where: adherenceWhereUniqueInput
    update: XOR<adherenceUpdateWithoutUserInput, adherenceUncheckedUpdateWithoutUserInput>
    create: XOR<adherenceCreateWithoutUserInput, adherenceUncheckedCreateWithoutUserInput>
  }

  export type adherenceUpdateWithWhereUniqueWithoutUserInput = {
    where: adherenceWhereUniqueInput
    data: XOR<adherenceUpdateWithoutUserInput, adherenceUncheckedUpdateWithoutUserInput>
  }

  export type adherenceUpdateManyWithWhereWithoutUserInput = {
    where: adherenceScalarWhereInput
    data: XOR<adherenceUpdateManyMutationInput, adherenceUncheckedUpdateManyWithoutUserInput>
  }

  export type adherenceScalarWhereInput = {
    AND?: adherenceScalarWhereInput | adherenceScalarWhereInput[]
    OR?: adherenceScalarWhereInput[]
    NOT?: adherenceScalarWhereInput | adherenceScalarWhereInput[]
    id?: UuidFilter<"adherence"> | string
    user_id?: UuidFilter<"adherence"> | string
    medication_id?: UuidFilter<"adherence"> | string
    scheduled_time?: StringFilter<"adherence"> | string
    scheduled_date?: DateTimeFilter<"adherence"> | Date | string
    taken_time?: DateTimeNullableFilter<"adherence"> | Date | string | null
    status?: StringNullableFilter<"adherence"> | string | null
    notes?: StringNullableFilter<"adherence"> | string | null
    reminder_sent?: BoolNullableFilter<"adherence"> | boolean | null
    side_effects_reported?: StringNullableListFilter<"adherence">
    dosage_taken?: JsonNullableFilter<"adherence">
    created_at?: DateTimeNullableFilter<"adherence"> | Date | string | null
    updated_at?: DateTimeNullableFilter<"adherence"> | Date | string | null
  }

  export type medicationsUpsertWithWhereUniqueWithoutUserInput = {
    where: medicationsWhereUniqueInput
    update: XOR<medicationsUpdateWithoutUserInput, medicationsUncheckedUpdateWithoutUserInput>
    create: XOR<medicationsCreateWithoutUserInput, medicationsUncheckedCreateWithoutUserInput>
  }

  export type medicationsUpdateWithWhereUniqueWithoutUserInput = {
    where: medicationsWhereUniqueInput
    data: XOR<medicationsUpdateWithoutUserInput, medicationsUncheckedUpdateWithoutUserInput>
  }

  export type medicationsUpdateManyWithWhereWithoutUserInput = {
    where: medicationsScalarWhereInput
    data: XOR<medicationsUpdateManyMutationInput, medicationsUncheckedUpdateManyWithoutUserInput>
  }

  export type medicationsScalarWhereInput = {
    AND?: medicationsScalarWhereInput | medicationsScalarWhereInput[]
    OR?: medicationsScalarWhereInput[]
    NOT?: medicationsScalarWhereInput | medicationsScalarWhereInput[]
    id?: UuidFilter<"medications"> | string
    user_id?: UuidFilter<"medications"> | string
    name?: StringFilter<"medications"> | string
    dosage?: JsonFilter<"medications">
    frequency?: JsonFilter<"medications">
    scheduled_times?: StringNullableListFilter<"medications">
    instructions?: StringNullableFilter<"medications"> | string | null
    start_date?: DateTimeNullableFilter<"medications"> | Date | string | null
    end_date?: DateTimeNullableFilter<"medications"> | Date | string | null
    refill_reminder?: JsonNullableFilter<"medications">
    side_effects_to_watch?: StringNullableListFilter<"medications">
    active?: BoolNullableFilter<"medications"> | boolean | null
    medication_type?: StringNullableFilter<"medications"> | string | null
    image_url?: StringNullableFilter<"medications"> | string | null
    created_at?: DateTimeNullableFilter<"medications"> | Date | string | null
    updated_at?: DateTimeNullableFilter<"medications"> | Date | string | null
  }

  export type remindersUpsertWithWhereUniqueWithoutUserInput = {
    where: remindersWhereUniqueInput
    update: XOR<remindersUpdateWithoutUserInput, remindersUncheckedUpdateWithoutUserInput>
    create: XOR<remindersCreateWithoutUserInput, remindersUncheckedCreateWithoutUserInput>
  }

  export type remindersUpdateWithWhereUniqueWithoutUserInput = {
    where: remindersWhereUniqueInput
    data: XOR<remindersUpdateWithoutUserInput, remindersUncheckedUpdateWithoutUserInput>
  }

  export type remindersUpdateManyWithWhereWithoutUserInput = {
    where: remindersScalarWhereInput
    data: XOR<remindersUpdateManyMutationInput, remindersUncheckedUpdateManyWithoutUserInput>
  }

  export type remindersScalarWhereInput = {
    AND?: remindersScalarWhereInput | remindersScalarWhereInput[]
    OR?: remindersScalarWhereInput[]
    NOT?: remindersScalarWhereInput | remindersScalarWhereInput[]
    id?: UuidFilter<"reminders"> | string
    user_id?: UuidFilter<"reminders"> | string
    medication_id?: UuidFilter<"reminders"> | string
    scheduled_time?: StringFilter<"reminders"> | string
    scheduled_date?: DateTimeFilter<"reminders"> | Date | string
    status?: StringNullableFilter<"reminders"> | string | null
    channels?: JsonNullableFilter<"reminders">
    message?: StringNullableFilter<"reminders"> | string | null
    retry_count?: IntNullableFilter<"reminders"> | number | null
    last_retry?: DateTimeNullableFilter<"reminders"> | Date | string | null
    adherence_id?: UuidNullableFilter<"reminders"> | string | null
    created_at?: DateTimeNullableFilter<"reminders"> | Date | string | null
    updated_at?: DateTimeNullableFilter<"reminders"> | Date | string | null
  }

  export type user_settingsUpsertWithoutUserInput = {
    update: XOR<user_settingsUpdateWithoutUserInput, user_settingsUncheckedUpdateWithoutUserInput>
    create: XOR<user_settingsCreateWithoutUserInput, user_settingsUncheckedCreateWithoutUserInput>
    where?: user_settingsWhereInput
  }

  export type user_settingsUpdateToOneWithWhereWithoutUserInput = {
    where?: user_settingsWhereInput
    data: XOR<user_settingsUpdateWithoutUserInput, user_settingsUncheckedUpdateWithoutUserInput>
  }

  export type user_settingsUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    email_enabled?: BoolFieldUpdateOperationsInput | boolean
    preferred_times?: user_settingsUpdatepreferred_timesInput | string[]
    timezone?: StringFieldUpdateOperationsInput | string
    notification_preferences?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type user_settingsUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    email_enabled?: BoolFieldUpdateOperationsInput | boolean
    preferred_times?: user_settingsUpdatepreferred_timesInput | string[]
    timezone?: StringFieldUpdateOperationsInput | string
    notification_preferences?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type usersCreateWithoutSettingsInput = {
    id?: string
    email?: string | null
    name?: string | null
    password?: string | null
    date_of_birth?: Date | string | null
    gender?: string | null
    allergies?: usersCreateallergiesInput | string[]
    conditions?: usersCreateconditionsInput | string[]
    is_admin?: boolean | null
    phone_number?: string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
    subscription_status?: string | null
    subscription_plan?: string | null
    subscription_expires_at?: Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
    adherence?: adherenceCreateNestedManyWithoutUserInput
    medication?: medicationsCreateNestedManyWithoutUserInput
    reminders?: remindersCreateNestedManyWithoutUserInput
  }

  export type usersUncheckedCreateWithoutSettingsInput = {
    id?: string
    email?: string | null
    name?: string | null
    password?: string | null
    date_of_birth?: Date | string | null
    gender?: string | null
    allergies?: usersCreateallergiesInput | string[]
    conditions?: usersCreateconditionsInput | string[]
    is_admin?: boolean | null
    phone_number?: string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
    subscription_status?: string | null
    subscription_plan?: string | null
    subscription_expires_at?: Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
    adherence?: adherenceUncheckedCreateNestedManyWithoutUserInput
    medication?: medicationsUncheckedCreateNestedManyWithoutUserInput
    reminders?: remindersUncheckedCreateNestedManyWithoutUserInput
  }

  export type usersCreateOrConnectWithoutSettingsInput = {
    where: usersWhereUniqueInput
    create: XOR<usersCreateWithoutSettingsInput, usersUncheckedCreateWithoutSettingsInput>
  }

  export type usersUpsertWithoutSettingsInput = {
    update: XOR<usersUpdateWithoutSettingsInput, usersUncheckedUpdateWithoutSettingsInput>
    create: XOR<usersCreateWithoutSettingsInput, usersUncheckedCreateWithoutSettingsInput>
    where?: usersWhereInput
  }

  export type usersUpdateToOneWithWhereWithoutSettingsInput = {
    where?: usersWhereInput
    data: XOR<usersUpdateWithoutSettingsInput, usersUncheckedUpdateWithoutSettingsInput>
  }

  export type usersUpdateWithoutSettingsInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    date_of_birth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    gender?: NullableStringFieldUpdateOperationsInput | string | null
    allergies?: usersUpdateallergiesInput | string[]
    conditions?: usersUpdateconditionsInput | string[]
    is_admin?: NullableBoolFieldUpdateOperationsInput | boolean | null
    phone_number?: NullableStringFieldUpdateOperationsInput | string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_status?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_plan?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_expires_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
    adherence?: adherenceUpdateManyWithoutUserNestedInput
    medication?: medicationsUpdateManyWithoutUserNestedInput
    reminders?: remindersUpdateManyWithoutUserNestedInput
  }

  export type usersUncheckedUpdateWithoutSettingsInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    date_of_birth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    gender?: NullableStringFieldUpdateOperationsInput | string | null
    allergies?: usersUpdateallergiesInput | string[]
    conditions?: usersUpdateconditionsInput | string[]
    is_admin?: NullableBoolFieldUpdateOperationsInput | boolean | null
    phone_number?: NullableStringFieldUpdateOperationsInput | string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_status?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_plan?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_expires_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
    adherence?: adherenceUncheckedUpdateManyWithoutUserNestedInput
    medication?: medicationsUncheckedUpdateManyWithoutUserNestedInput
    reminders?: remindersUncheckedUpdateManyWithoutUserNestedInput
  }

  export type adherenceCreateWithoutMedicationInput = {
    id?: string
    scheduled_time: string
    scheduled_date: Date | string
    taken_time?: Date | string | null
    status?: string | null
    notes?: string | null
    reminder_sent?: boolean | null
    side_effects_reported?: adherenceCreateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
    user: usersCreateNestedOneWithoutAdherenceInput
    reminders?: remindersCreateNestedManyWithoutAdherenceInput
  }

  export type adherenceUncheckedCreateWithoutMedicationInput = {
    id?: string
    user_id: string
    scheduled_time: string
    scheduled_date: Date | string
    taken_time?: Date | string | null
    status?: string | null
    notes?: string | null
    reminder_sent?: boolean | null
    side_effects_reported?: adherenceCreateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
    reminders?: remindersUncheckedCreateNestedManyWithoutAdherenceInput
  }

  export type adherenceCreateOrConnectWithoutMedicationInput = {
    where: adherenceWhereUniqueInput
    create: XOR<adherenceCreateWithoutMedicationInput, adherenceUncheckedCreateWithoutMedicationInput>
  }

  export type adherenceCreateManyMedicationInputEnvelope = {
    data: adherenceCreateManyMedicationInput | adherenceCreateManyMedicationInput[]
    skipDuplicates?: boolean
  }

  export type usersCreateWithoutMedicationInput = {
    id?: string
    email?: string | null
    name?: string | null
    password?: string | null
    date_of_birth?: Date | string | null
    gender?: string | null
    allergies?: usersCreateallergiesInput | string[]
    conditions?: usersCreateconditionsInput | string[]
    is_admin?: boolean | null
    phone_number?: string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
    subscription_status?: string | null
    subscription_plan?: string | null
    subscription_expires_at?: Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
    adherence?: adherenceCreateNestedManyWithoutUserInput
    reminders?: remindersCreateNestedManyWithoutUserInput
    settings?: user_settingsCreateNestedOneWithoutUserInput
  }

  export type usersUncheckedCreateWithoutMedicationInput = {
    id?: string
    email?: string | null
    name?: string | null
    password?: string | null
    date_of_birth?: Date | string | null
    gender?: string | null
    allergies?: usersCreateallergiesInput | string[]
    conditions?: usersCreateconditionsInput | string[]
    is_admin?: boolean | null
    phone_number?: string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
    subscription_status?: string | null
    subscription_plan?: string | null
    subscription_expires_at?: Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
    adherence?: adherenceUncheckedCreateNestedManyWithoutUserInput
    reminders?: remindersUncheckedCreateNestedManyWithoutUserInput
    settings?: user_settingsUncheckedCreateNestedOneWithoutUserInput
  }

  export type usersCreateOrConnectWithoutMedicationInput = {
    where: usersWhereUniqueInput
    create: XOR<usersCreateWithoutMedicationInput, usersUncheckedCreateWithoutMedicationInput>
  }

  export type remindersCreateWithoutMedicationInput = {
    id?: string
    scheduled_time: string
    scheduled_date: Date | string
    status?: string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: string | null
    retry_count?: number | null
    last_retry?: Date | string | null
    created_at?: Date | string | null
    updated_at?: Date | string | null
    adherence?: adherenceCreateNestedOneWithoutRemindersInput
    user: usersCreateNestedOneWithoutRemindersInput
  }

  export type remindersUncheckedCreateWithoutMedicationInput = {
    id?: string
    user_id: string
    scheduled_time: string
    scheduled_date: Date | string
    status?: string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: string | null
    retry_count?: number | null
    last_retry?: Date | string | null
    adherence_id?: string | null
    created_at?: Date | string | null
    updated_at?: Date | string | null
  }

  export type remindersCreateOrConnectWithoutMedicationInput = {
    where: remindersWhereUniqueInput
    create: XOR<remindersCreateWithoutMedicationInput, remindersUncheckedCreateWithoutMedicationInput>
  }

  export type remindersCreateManyMedicationInputEnvelope = {
    data: remindersCreateManyMedicationInput | remindersCreateManyMedicationInput[]
    skipDuplicates?: boolean
  }

  export type adherenceUpsertWithWhereUniqueWithoutMedicationInput = {
    where: adherenceWhereUniqueInput
    update: XOR<adherenceUpdateWithoutMedicationInput, adherenceUncheckedUpdateWithoutMedicationInput>
    create: XOR<adherenceCreateWithoutMedicationInput, adherenceUncheckedCreateWithoutMedicationInput>
  }

  export type adherenceUpdateWithWhereUniqueWithoutMedicationInput = {
    where: adherenceWhereUniqueInput
    data: XOR<adherenceUpdateWithoutMedicationInput, adherenceUncheckedUpdateWithoutMedicationInput>
  }

  export type adherenceUpdateManyWithWhereWithoutMedicationInput = {
    where: adherenceScalarWhereInput
    data: XOR<adherenceUpdateManyMutationInput, adherenceUncheckedUpdateManyWithoutMedicationInput>
  }

  export type usersUpsertWithoutMedicationInput = {
    update: XOR<usersUpdateWithoutMedicationInput, usersUncheckedUpdateWithoutMedicationInput>
    create: XOR<usersCreateWithoutMedicationInput, usersUncheckedCreateWithoutMedicationInput>
    where?: usersWhereInput
  }

  export type usersUpdateToOneWithWhereWithoutMedicationInput = {
    where?: usersWhereInput
    data: XOR<usersUpdateWithoutMedicationInput, usersUncheckedUpdateWithoutMedicationInput>
  }

  export type usersUpdateWithoutMedicationInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    date_of_birth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    gender?: NullableStringFieldUpdateOperationsInput | string | null
    allergies?: usersUpdateallergiesInput | string[]
    conditions?: usersUpdateconditionsInput | string[]
    is_admin?: NullableBoolFieldUpdateOperationsInput | boolean | null
    phone_number?: NullableStringFieldUpdateOperationsInput | string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_status?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_plan?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_expires_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
    adherence?: adherenceUpdateManyWithoutUserNestedInput
    reminders?: remindersUpdateManyWithoutUserNestedInput
    settings?: user_settingsUpdateOneWithoutUserNestedInput
  }

  export type usersUncheckedUpdateWithoutMedicationInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    date_of_birth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    gender?: NullableStringFieldUpdateOperationsInput | string | null
    allergies?: usersUpdateallergiesInput | string[]
    conditions?: usersUpdateconditionsInput | string[]
    is_admin?: NullableBoolFieldUpdateOperationsInput | boolean | null
    phone_number?: NullableStringFieldUpdateOperationsInput | string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_status?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_plan?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_expires_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
    adherence?: adherenceUncheckedUpdateManyWithoutUserNestedInput
    reminders?: remindersUncheckedUpdateManyWithoutUserNestedInput
    settings?: user_settingsUncheckedUpdateOneWithoutUserNestedInput
  }

  export type remindersUpsertWithWhereUniqueWithoutMedicationInput = {
    where: remindersWhereUniqueInput
    update: XOR<remindersUpdateWithoutMedicationInput, remindersUncheckedUpdateWithoutMedicationInput>
    create: XOR<remindersCreateWithoutMedicationInput, remindersUncheckedCreateWithoutMedicationInput>
  }

  export type remindersUpdateWithWhereUniqueWithoutMedicationInput = {
    where: remindersWhereUniqueInput
    data: XOR<remindersUpdateWithoutMedicationInput, remindersUncheckedUpdateWithoutMedicationInput>
  }

  export type remindersUpdateManyWithWhereWithoutMedicationInput = {
    where: remindersScalarWhereInput
    data: XOR<remindersUpdateManyMutationInput, remindersUncheckedUpdateManyWithoutMedicationInput>
  }

  export type medicationsCreateWithoutAdherenceInput = {
    id?: string
    name: string
    dosage: JsonNullValueInput | InputJsonValue
    frequency: JsonNullValueInput | InputJsonValue
    scheduled_times?: medicationsCreatescheduled_timesInput | string[]
    instructions?: string | null
    start_date?: Date | string | null
    end_date?: Date | string | null
    refill_reminder?: NullableJsonNullValueInput | InputJsonValue
    side_effects_to_watch?: medicationsCreateside_effects_to_watchInput | string[]
    active?: boolean | null
    medication_type?: string | null
    image_url?: string | null
    created_at?: Date | string | null
    updated_at?: Date | string | null
    user: usersCreateNestedOneWithoutMedicationInput
    reminders?: remindersCreateNestedManyWithoutMedicationInput
  }

  export type medicationsUncheckedCreateWithoutAdherenceInput = {
    id?: string
    user_id: string
    name: string
    dosage: JsonNullValueInput | InputJsonValue
    frequency: JsonNullValueInput | InputJsonValue
    scheduled_times?: medicationsCreatescheduled_timesInput | string[]
    instructions?: string | null
    start_date?: Date | string | null
    end_date?: Date | string | null
    refill_reminder?: NullableJsonNullValueInput | InputJsonValue
    side_effects_to_watch?: medicationsCreateside_effects_to_watchInput | string[]
    active?: boolean | null
    medication_type?: string | null
    image_url?: string | null
    created_at?: Date | string | null
    updated_at?: Date | string | null
    reminders?: remindersUncheckedCreateNestedManyWithoutMedicationInput
  }

  export type medicationsCreateOrConnectWithoutAdherenceInput = {
    where: medicationsWhereUniqueInput
    create: XOR<medicationsCreateWithoutAdherenceInput, medicationsUncheckedCreateWithoutAdherenceInput>
  }

  export type usersCreateWithoutAdherenceInput = {
    id?: string
    email?: string | null
    name?: string | null
    password?: string | null
    date_of_birth?: Date | string | null
    gender?: string | null
    allergies?: usersCreateallergiesInput | string[]
    conditions?: usersCreateconditionsInput | string[]
    is_admin?: boolean | null
    phone_number?: string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
    subscription_status?: string | null
    subscription_plan?: string | null
    subscription_expires_at?: Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
    medication?: medicationsCreateNestedManyWithoutUserInput
    reminders?: remindersCreateNestedManyWithoutUserInput
    settings?: user_settingsCreateNestedOneWithoutUserInput
  }

  export type usersUncheckedCreateWithoutAdherenceInput = {
    id?: string
    email?: string | null
    name?: string | null
    password?: string | null
    date_of_birth?: Date | string | null
    gender?: string | null
    allergies?: usersCreateallergiesInput | string[]
    conditions?: usersCreateconditionsInput | string[]
    is_admin?: boolean | null
    phone_number?: string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
    subscription_status?: string | null
    subscription_plan?: string | null
    subscription_expires_at?: Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
    medication?: medicationsUncheckedCreateNestedManyWithoutUserInput
    reminders?: remindersUncheckedCreateNestedManyWithoutUserInput
    settings?: user_settingsUncheckedCreateNestedOneWithoutUserInput
  }

  export type usersCreateOrConnectWithoutAdherenceInput = {
    where: usersWhereUniqueInput
    create: XOR<usersCreateWithoutAdherenceInput, usersUncheckedCreateWithoutAdherenceInput>
  }

  export type remindersCreateWithoutAdherenceInput = {
    id?: string
    scheduled_time: string
    scheduled_date: Date | string
    status?: string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: string | null
    retry_count?: number | null
    last_retry?: Date | string | null
    created_at?: Date | string | null
    updated_at?: Date | string | null
    medication: medicationsCreateNestedOneWithoutRemindersInput
    user: usersCreateNestedOneWithoutRemindersInput
  }

  export type remindersUncheckedCreateWithoutAdherenceInput = {
    id?: string
    user_id: string
    medication_id: string
    scheduled_time: string
    scheduled_date: Date | string
    status?: string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: string | null
    retry_count?: number | null
    last_retry?: Date | string | null
    created_at?: Date | string | null
    updated_at?: Date | string | null
  }

  export type remindersCreateOrConnectWithoutAdherenceInput = {
    where: remindersWhereUniqueInput
    create: XOR<remindersCreateWithoutAdherenceInput, remindersUncheckedCreateWithoutAdherenceInput>
  }

  export type remindersCreateManyAdherenceInputEnvelope = {
    data: remindersCreateManyAdherenceInput | remindersCreateManyAdherenceInput[]
    skipDuplicates?: boolean
  }

  export type medicationsUpsertWithoutAdherenceInput = {
    update: XOR<medicationsUpdateWithoutAdherenceInput, medicationsUncheckedUpdateWithoutAdherenceInput>
    create: XOR<medicationsCreateWithoutAdherenceInput, medicationsUncheckedCreateWithoutAdherenceInput>
    where?: medicationsWhereInput
  }

  export type medicationsUpdateToOneWithWhereWithoutAdherenceInput = {
    where?: medicationsWhereInput
    data: XOR<medicationsUpdateWithoutAdherenceInput, medicationsUncheckedUpdateWithoutAdherenceInput>
  }

  export type medicationsUpdateWithoutAdherenceInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dosage?: JsonNullValueInput | InputJsonValue
    frequency?: JsonNullValueInput | InputJsonValue
    scheduled_times?: medicationsUpdatescheduled_timesInput | string[]
    instructions?: NullableStringFieldUpdateOperationsInput | string | null
    start_date?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    end_date?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    refill_reminder?: NullableJsonNullValueInput | InputJsonValue
    side_effects_to_watch?: medicationsUpdateside_effects_to_watchInput | string[]
    active?: NullableBoolFieldUpdateOperationsInput | boolean | null
    medication_type?: NullableStringFieldUpdateOperationsInput | string | null
    image_url?: NullableStringFieldUpdateOperationsInput | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    user?: usersUpdateOneRequiredWithoutMedicationNestedInput
    reminders?: remindersUpdateManyWithoutMedicationNestedInput
  }

  export type medicationsUncheckedUpdateWithoutAdherenceInput = {
    id?: StringFieldUpdateOperationsInput | string
    user_id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dosage?: JsonNullValueInput | InputJsonValue
    frequency?: JsonNullValueInput | InputJsonValue
    scheduled_times?: medicationsUpdatescheduled_timesInput | string[]
    instructions?: NullableStringFieldUpdateOperationsInput | string | null
    start_date?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    end_date?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    refill_reminder?: NullableJsonNullValueInput | InputJsonValue
    side_effects_to_watch?: medicationsUpdateside_effects_to_watchInput | string[]
    active?: NullableBoolFieldUpdateOperationsInput | boolean | null
    medication_type?: NullableStringFieldUpdateOperationsInput | string | null
    image_url?: NullableStringFieldUpdateOperationsInput | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    reminders?: remindersUncheckedUpdateManyWithoutMedicationNestedInput
  }

  export type usersUpsertWithoutAdherenceInput = {
    update: XOR<usersUpdateWithoutAdherenceInput, usersUncheckedUpdateWithoutAdherenceInput>
    create: XOR<usersCreateWithoutAdherenceInput, usersUncheckedCreateWithoutAdherenceInput>
    where?: usersWhereInput
  }

  export type usersUpdateToOneWithWhereWithoutAdherenceInput = {
    where?: usersWhereInput
    data: XOR<usersUpdateWithoutAdherenceInput, usersUncheckedUpdateWithoutAdherenceInput>
  }

  export type usersUpdateWithoutAdherenceInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    date_of_birth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    gender?: NullableStringFieldUpdateOperationsInput | string | null
    allergies?: usersUpdateallergiesInput | string[]
    conditions?: usersUpdateconditionsInput | string[]
    is_admin?: NullableBoolFieldUpdateOperationsInput | boolean | null
    phone_number?: NullableStringFieldUpdateOperationsInput | string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_status?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_plan?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_expires_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
    medication?: medicationsUpdateManyWithoutUserNestedInput
    reminders?: remindersUpdateManyWithoutUserNestedInput
    settings?: user_settingsUpdateOneWithoutUserNestedInput
  }

  export type usersUncheckedUpdateWithoutAdherenceInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    date_of_birth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    gender?: NullableStringFieldUpdateOperationsInput | string | null
    allergies?: usersUpdateallergiesInput | string[]
    conditions?: usersUpdateconditionsInput | string[]
    is_admin?: NullableBoolFieldUpdateOperationsInput | boolean | null
    phone_number?: NullableStringFieldUpdateOperationsInput | string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_status?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_plan?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_expires_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
    medication?: medicationsUncheckedUpdateManyWithoutUserNestedInput
    reminders?: remindersUncheckedUpdateManyWithoutUserNestedInput
    settings?: user_settingsUncheckedUpdateOneWithoutUserNestedInput
  }

  export type remindersUpsertWithWhereUniqueWithoutAdherenceInput = {
    where: remindersWhereUniqueInput
    update: XOR<remindersUpdateWithoutAdherenceInput, remindersUncheckedUpdateWithoutAdherenceInput>
    create: XOR<remindersCreateWithoutAdherenceInput, remindersUncheckedCreateWithoutAdherenceInput>
  }

  export type remindersUpdateWithWhereUniqueWithoutAdherenceInput = {
    where: remindersWhereUniqueInput
    data: XOR<remindersUpdateWithoutAdherenceInput, remindersUncheckedUpdateWithoutAdherenceInput>
  }

  export type remindersUpdateManyWithWhereWithoutAdherenceInput = {
    where: remindersScalarWhereInput
    data: XOR<remindersUpdateManyMutationInput, remindersUncheckedUpdateManyWithoutAdherenceInput>
  }

  export type adherenceCreateWithoutRemindersInput = {
    id?: string
    scheduled_time: string
    scheduled_date: Date | string
    taken_time?: Date | string | null
    status?: string | null
    notes?: string | null
    reminder_sent?: boolean | null
    side_effects_reported?: adherenceCreateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
    medication: medicationsCreateNestedOneWithoutAdherenceInput
    user: usersCreateNestedOneWithoutAdherenceInput
  }

  export type adherenceUncheckedCreateWithoutRemindersInput = {
    id?: string
    user_id: string
    medication_id: string
    scheduled_time: string
    scheduled_date: Date | string
    taken_time?: Date | string | null
    status?: string | null
    notes?: string | null
    reminder_sent?: boolean | null
    side_effects_reported?: adherenceCreateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
  }

  export type adherenceCreateOrConnectWithoutRemindersInput = {
    where: adherenceWhereUniqueInput
    create: XOR<adherenceCreateWithoutRemindersInput, adherenceUncheckedCreateWithoutRemindersInput>
  }

  export type medicationsCreateWithoutRemindersInput = {
    id?: string
    name: string
    dosage: JsonNullValueInput | InputJsonValue
    frequency: JsonNullValueInput | InputJsonValue
    scheduled_times?: medicationsCreatescheduled_timesInput | string[]
    instructions?: string | null
    start_date?: Date | string | null
    end_date?: Date | string | null
    refill_reminder?: NullableJsonNullValueInput | InputJsonValue
    side_effects_to_watch?: medicationsCreateside_effects_to_watchInput | string[]
    active?: boolean | null
    medication_type?: string | null
    image_url?: string | null
    created_at?: Date | string | null
    updated_at?: Date | string | null
    adherence?: adherenceCreateNestedManyWithoutMedicationInput
    user: usersCreateNestedOneWithoutMedicationInput
  }

  export type medicationsUncheckedCreateWithoutRemindersInput = {
    id?: string
    user_id: string
    name: string
    dosage: JsonNullValueInput | InputJsonValue
    frequency: JsonNullValueInput | InputJsonValue
    scheduled_times?: medicationsCreatescheduled_timesInput | string[]
    instructions?: string | null
    start_date?: Date | string | null
    end_date?: Date | string | null
    refill_reminder?: NullableJsonNullValueInput | InputJsonValue
    side_effects_to_watch?: medicationsCreateside_effects_to_watchInput | string[]
    active?: boolean | null
    medication_type?: string | null
    image_url?: string | null
    created_at?: Date | string | null
    updated_at?: Date | string | null
    adherence?: adherenceUncheckedCreateNestedManyWithoutMedicationInput
  }

  export type medicationsCreateOrConnectWithoutRemindersInput = {
    where: medicationsWhereUniqueInput
    create: XOR<medicationsCreateWithoutRemindersInput, medicationsUncheckedCreateWithoutRemindersInput>
  }

  export type usersCreateWithoutRemindersInput = {
    id?: string
    email?: string | null
    name?: string | null
    password?: string | null
    date_of_birth?: Date | string | null
    gender?: string | null
    allergies?: usersCreateallergiesInput | string[]
    conditions?: usersCreateconditionsInput | string[]
    is_admin?: boolean | null
    phone_number?: string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
    subscription_status?: string | null
    subscription_plan?: string | null
    subscription_expires_at?: Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
    adherence?: adherenceCreateNestedManyWithoutUserInput
    medication?: medicationsCreateNestedManyWithoutUserInput
    settings?: user_settingsCreateNestedOneWithoutUserInput
  }

  export type usersUncheckedCreateWithoutRemindersInput = {
    id?: string
    email?: string | null
    name?: string | null
    password?: string | null
    date_of_birth?: Date | string | null
    gender?: string | null
    allergies?: usersCreateallergiesInput | string[]
    conditions?: usersCreateconditionsInput | string[]
    is_admin?: boolean | null
    phone_number?: string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
    subscription_status?: string | null
    subscription_plan?: string | null
    subscription_expires_at?: Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
    adherence?: adherenceUncheckedCreateNestedManyWithoutUserInput
    medication?: medicationsUncheckedCreateNestedManyWithoutUserInput
    settings?: user_settingsUncheckedCreateNestedOneWithoutUserInput
  }

  export type usersCreateOrConnectWithoutRemindersInput = {
    where: usersWhereUniqueInput
    create: XOR<usersCreateWithoutRemindersInput, usersUncheckedCreateWithoutRemindersInput>
  }

  export type adherenceUpsertWithoutRemindersInput = {
    update: XOR<adherenceUpdateWithoutRemindersInput, adherenceUncheckedUpdateWithoutRemindersInput>
    create: XOR<adherenceCreateWithoutRemindersInput, adherenceUncheckedCreateWithoutRemindersInput>
    where?: adherenceWhereInput
  }

  export type adherenceUpdateToOneWithWhereWithoutRemindersInput = {
    where?: adherenceWhereInput
    data: XOR<adherenceUpdateWithoutRemindersInput, adherenceUncheckedUpdateWithoutRemindersInput>
  }

  export type adherenceUpdateWithoutRemindersInput = {
    id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    taken_time?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    status?: NullableStringFieldUpdateOperationsInput | string | null
    notes?: NullableStringFieldUpdateOperationsInput | string | null
    reminder_sent?: NullableBoolFieldUpdateOperationsInput | boolean | null
    side_effects_reported?: adherenceUpdateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    medication?: medicationsUpdateOneRequiredWithoutAdherenceNestedInput
    user?: usersUpdateOneRequiredWithoutAdherenceNestedInput
  }

  export type adherenceUncheckedUpdateWithoutRemindersInput = {
    id?: StringFieldUpdateOperationsInput | string
    user_id?: StringFieldUpdateOperationsInput | string
    medication_id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    taken_time?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    status?: NullableStringFieldUpdateOperationsInput | string | null
    notes?: NullableStringFieldUpdateOperationsInput | string | null
    reminder_sent?: NullableBoolFieldUpdateOperationsInput | boolean | null
    side_effects_reported?: adherenceUpdateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type medicationsUpsertWithoutRemindersInput = {
    update: XOR<medicationsUpdateWithoutRemindersInput, medicationsUncheckedUpdateWithoutRemindersInput>
    create: XOR<medicationsCreateWithoutRemindersInput, medicationsUncheckedCreateWithoutRemindersInput>
    where?: medicationsWhereInput
  }

  export type medicationsUpdateToOneWithWhereWithoutRemindersInput = {
    where?: medicationsWhereInput
    data: XOR<medicationsUpdateWithoutRemindersInput, medicationsUncheckedUpdateWithoutRemindersInput>
  }

  export type medicationsUpdateWithoutRemindersInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dosage?: JsonNullValueInput | InputJsonValue
    frequency?: JsonNullValueInput | InputJsonValue
    scheduled_times?: medicationsUpdatescheduled_timesInput | string[]
    instructions?: NullableStringFieldUpdateOperationsInput | string | null
    start_date?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    end_date?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    refill_reminder?: NullableJsonNullValueInput | InputJsonValue
    side_effects_to_watch?: medicationsUpdateside_effects_to_watchInput | string[]
    active?: NullableBoolFieldUpdateOperationsInput | boolean | null
    medication_type?: NullableStringFieldUpdateOperationsInput | string | null
    image_url?: NullableStringFieldUpdateOperationsInput | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    adherence?: adherenceUpdateManyWithoutMedicationNestedInput
    user?: usersUpdateOneRequiredWithoutMedicationNestedInput
  }

  export type medicationsUncheckedUpdateWithoutRemindersInput = {
    id?: StringFieldUpdateOperationsInput | string
    user_id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dosage?: JsonNullValueInput | InputJsonValue
    frequency?: JsonNullValueInput | InputJsonValue
    scheduled_times?: medicationsUpdatescheduled_timesInput | string[]
    instructions?: NullableStringFieldUpdateOperationsInput | string | null
    start_date?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    end_date?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    refill_reminder?: NullableJsonNullValueInput | InputJsonValue
    side_effects_to_watch?: medicationsUpdateside_effects_to_watchInput | string[]
    active?: NullableBoolFieldUpdateOperationsInput | boolean | null
    medication_type?: NullableStringFieldUpdateOperationsInput | string | null
    image_url?: NullableStringFieldUpdateOperationsInput | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    adherence?: adherenceUncheckedUpdateManyWithoutMedicationNestedInput
  }

  export type usersUpsertWithoutRemindersInput = {
    update: XOR<usersUpdateWithoutRemindersInput, usersUncheckedUpdateWithoutRemindersInput>
    create: XOR<usersCreateWithoutRemindersInput, usersUncheckedCreateWithoutRemindersInput>
    where?: usersWhereInput
  }

  export type usersUpdateToOneWithWhereWithoutRemindersInput = {
    where?: usersWhereInput
    data: XOR<usersUpdateWithoutRemindersInput, usersUncheckedUpdateWithoutRemindersInput>
  }

  export type usersUpdateWithoutRemindersInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    date_of_birth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    gender?: NullableStringFieldUpdateOperationsInput | string | null
    allergies?: usersUpdateallergiesInput | string[]
    conditions?: usersUpdateconditionsInput | string[]
    is_admin?: NullableBoolFieldUpdateOperationsInput | boolean | null
    phone_number?: NullableStringFieldUpdateOperationsInput | string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_status?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_plan?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_expires_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
    adherence?: adherenceUpdateManyWithoutUserNestedInput
    medication?: medicationsUpdateManyWithoutUserNestedInput
    settings?: user_settingsUpdateOneWithoutUserNestedInput
  }

  export type usersUncheckedUpdateWithoutRemindersInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    date_of_birth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    gender?: NullableStringFieldUpdateOperationsInput | string | null
    allergies?: usersUpdateallergiesInput | string[]
    conditions?: usersUpdateconditionsInput | string[]
    is_admin?: NullableBoolFieldUpdateOperationsInput | boolean | null
    phone_number?: NullableStringFieldUpdateOperationsInput | string | null
    emergency_contact?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_status?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_plan?: NullableStringFieldUpdateOperationsInput | string | null
    subscription_expires_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    subscription_features?: NullableJsonNullValueInput | InputJsonValue
    adherence?: adherenceUncheckedUpdateManyWithoutUserNestedInput
    medication?: medicationsUncheckedUpdateManyWithoutUserNestedInput
    settings?: user_settingsUncheckedUpdateOneWithoutUserNestedInput
  }

  export type adherenceCreateManyUserInput = {
    id?: string
    medication_id: string
    scheduled_time: string
    scheduled_date: Date | string
    taken_time?: Date | string | null
    status?: string | null
    notes?: string | null
    reminder_sent?: boolean | null
    side_effects_reported?: adherenceCreateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
  }

  export type medicationsCreateManyUserInput = {
    id?: string
    name: string
    dosage: JsonNullValueInput | InputJsonValue
    frequency: JsonNullValueInput | InputJsonValue
    scheduled_times?: medicationsCreatescheduled_timesInput | string[]
    instructions?: string | null
    start_date?: Date | string | null
    end_date?: Date | string | null
    refill_reminder?: NullableJsonNullValueInput | InputJsonValue
    side_effects_to_watch?: medicationsCreateside_effects_to_watchInput | string[]
    active?: boolean | null
    medication_type?: string | null
    image_url?: string | null
    created_at?: Date | string | null
    updated_at?: Date | string | null
  }

  export type remindersCreateManyUserInput = {
    id?: string
    medication_id: string
    scheduled_time: string
    scheduled_date: Date | string
    status?: string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: string | null
    retry_count?: number | null
    last_retry?: Date | string | null
    adherence_id?: string | null
    created_at?: Date | string | null
    updated_at?: Date | string | null
  }

  export type adherenceUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    taken_time?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    status?: NullableStringFieldUpdateOperationsInput | string | null
    notes?: NullableStringFieldUpdateOperationsInput | string | null
    reminder_sent?: NullableBoolFieldUpdateOperationsInput | boolean | null
    side_effects_reported?: adherenceUpdateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    medication?: medicationsUpdateOneRequiredWithoutAdherenceNestedInput
    reminders?: remindersUpdateManyWithoutAdherenceNestedInput
  }

  export type adherenceUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    medication_id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    taken_time?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    status?: NullableStringFieldUpdateOperationsInput | string | null
    notes?: NullableStringFieldUpdateOperationsInput | string | null
    reminder_sent?: NullableBoolFieldUpdateOperationsInput | boolean | null
    side_effects_reported?: adherenceUpdateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    reminders?: remindersUncheckedUpdateManyWithoutAdherenceNestedInput
  }

  export type adherenceUncheckedUpdateManyWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    medication_id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    taken_time?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    status?: NullableStringFieldUpdateOperationsInput | string | null
    notes?: NullableStringFieldUpdateOperationsInput | string | null
    reminder_sent?: NullableBoolFieldUpdateOperationsInput | boolean | null
    side_effects_reported?: adherenceUpdateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type medicationsUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dosage?: JsonNullValueInput | InputJsonValue
    frequency?: JsonNullValueInput | InputJsonValue
    scheduled_times?: medicationsUpdatescheduled_timesInput | string[]
    instructions?: NullableStringFieldUpdateOperationsInput | string | null
    start_date?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    end_date?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    refill_reminder?: NullableJsonNullValueInput | InputJsonValue
    side_effects_to_watch?: medicationsUpdateside_effects_to_watchInput | string[]
    active?: NullableBoolFieldUpdateOperationsInput | boolean | null
    medication_type?: NullableStringFieldUpdateOperationsInput | string | null
    image_url?: NullableStringFieldUpdateOperationsInput | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    adherence?: adherenceUpdateManyWithoutMedicationNestedInput
    reminders?: remindersUpdateManyWithoutMedicationNestedInput
  }

  export type medicationsUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dosage?: JsonNullValueInput | InputJsonValue
    frequency?: JsonNullValueInput | InputJsonValue
    scheduled_times?: medicationsUpdatescheduled_timesInput | string[]
    instructions?: NullableStringFieldUpdateOperationsInput | string | null
    start_date?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    end_date?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    refill_reminder?: NullableJsonNullValueInput | InputJsonValue
    side_effects_to_watch?: medicationsUpdateside_effects_to_watchInput | string[]
    active?: NullableBoolFieldUpdateOperationsInput | boolean | null
    medication_type?: NullableStringFieldUpdateOperationsInput | string | null
    image_url?: NullableStringFieldUpdateOperationsInput | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    adherence?: adherenceUncheckedUpdateManyWithoutMedicationNestedInput
    reminders?: remindersUncheckedUpdateManyWithoutMedicationNestedInput
  }

  export type medicationsUncheckedUpdateManyWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    dosage?: JsonNullValueInput | InputJsonValue
    frequency?: JsonNullValueInput | InputJsonValue
    scheduled_times?: medicationsUpdatescheduled_timesInput | string[]
    instructions?: NullableStringFieldUpdateOperationsInput | string | null
    start_date?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    end_date?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    refill_reminder?: NullableJsonNullValueInput | InputJsonValue
    side_effects_to_watch?: medicationsUpdateside_effects_to_watchInput | string[]
    active?: NullableBoolFieldUpdateOperationsInput | boolean | null
    medication_type?: NullableStringFieldUpdateOperationsInput | string | null
    image_url?: NullableStringFieldUpdateOperationsInput | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type remindersUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: NullableStringFieldUpdateOperationsInput | string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: NullableStringFieldUpdateOperationsInput | string | null
    retry_count?: NullableIntFieldUpdateOperationsInput | number | null
    last_retry?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    adherence?: adherenceUpdateOneWithoutRemindersNestedInput
    medication?: medicationsUpdateOneRequiredWithoutRemindersNestedInput
  }

  export type remindersUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    medication_id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: NullableStringFieldUpdateOperationsInput | string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: NullableStringFieldUpdateOperationsInput | string | null
    retry_count?: NullableIntFieldUpdateOperationsInput | number | null
    last_retry?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    adherence_id?: NullableStringFieldUpdateOperationsInput | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type remindersUncheckedUpdateManyWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    medication_id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: NullableStringFieldUpdateOperationsInput | string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: NullableStringFieldUpdateOperationsInput | string | null
    retry_count?: NullableIntFieldUpdateOperationsInput | number | null
    last_retry?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    adherence_id?: NullableStringFieldUpdateOperationsInput | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type adherenceCreateManyMedicationInput = {
    id?: string
    user_id: string
    scheduled_time: string
    scheduled_date: Date | string
    taken_time?: Date | string | null
    status?: string | null
    notes?: string | null
    reminder_sent?: boolean | null
    side_effects_reported?: adherenceCreateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: Date | string | null
    updated_at?: Date | string | null
  }

  export type remindersCreateManyMedicationInput = {
    id?: string
    user_id: string
    scheduled_time: string
    scheduled_date: Date | string
    status?: string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: string | null
    retry_count?: number | null
    last_retry?: Date | string | null
    adherence_id?: string | null
    created_at?: Date | string | null
    updated_at?: Date | string | null
  }

  export type adherenceUpdateWithoutMedicationInput = {
    id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    taken_time?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    status?: NullableStringFieldUpdateOperationsInput | string | null
    notes?: NullableStringFieldUpdateOperationsInput | string | null
    reminder_sent?: NullableBoolFieldUpdateOperationsInput | boolean | null
    side_effects_reported?: adherenceUpdateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    user?: usersUpdateOneRequiredWithoutAdherenceNestedInput
    reminders?: remindersUpdateManyWithoutAdherenceNestedInput
  }

  export type adherenceUncheckedUpdateWithoutMedicationInput = {
    id?: StringFieldUpdateOperationsInput | string
    user_id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    taken_time?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    status?: NullableStringFieldUpdateOperationsInput | string | null
    notes?: NullableStringFieldUpdateOperationsInput | string | null
    reminder_sent?: NullableBoolFieldUpdateOperationsInput | boolean | null
    side_effects_reported?: adherenceUpdateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    reminders?: remindersUncheckedUpdateManyWithoutAdherenceNestedInput
  }

  export type adherenceUncheckedUpdateManyWithoutMedicationInput = {
    id?: StringFieldUpdateOperationsInput | string
    user_id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    taken_time?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    status?: NullableStringFieldUpdateOperationsInput | string | null
    notes?: NullableStringFieldUpdateOperationsInput | string | null
    reminder_sent?: NullableBoolFieldUpdateOperationsInput | boolean | null
    side_effects_reported?: adherenceUpdateside_effects_reportedInput | string[]
    dosage_taken?: NullableJsonNullValueInput | InputJsonValue
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type remindersUpdateWithoutMedicationInput = {
    id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: NullableStringFieldUpdateOperationsInput | string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: NullableStringFieldUpdateOperationsInput | string | null
    retry_count?: NullableIntFieldUpdateOperationsInput | number | null
    last_retry?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    adherence?: adherenceUpdateOneWithoutRemindersNestedInput
    user?: usersUpdateOneRequiredWithoutRemindersNestedInput
  }

  export type remindersUncheckedUpdateWithoutMedicationInput = {
    id?: StringFieldUpdateOperationsInput | string
    user_id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: NullableStringFieldUpdateOperationsInput | string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: NullableStringFieldUpdateOperationsInput | string | null
    retry_count?: NullableIntFieldUpdateOperationsInput | number | null
    last_retry?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    adherence_id?: NullableStringFieldUpdateOperationsInput | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type remindersUncheckedUpdateManyWithoutMedicationInput = {
    id?: StringFieldUpdateOperationsInput | string
    user_id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: NullableStringFieldUpdateOperationsInput | string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: NullableStringFieldUpdateOperationsInput | string | null
    retry_count?: NullableIntFieldUpdateOperationsInput | number | null
    last_retry?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    adherence_id?: NullableStringFieldUpdateOperationsInput | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type remindersCreateManyAdherenceInput = {
    id?: string
    user_id: string
    medication_id: string
    scheduled_time: string
    scheduled_date: Date | string
    status?: string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: string | null
    retry_count?: number | null
    last_retry?: Date | string | null
    created_at?: Date | string | null
    updated_at?: Date | string | null
  }

  export type remindersUpdateWithoutAdherenceInput = {
    id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: NullableStringFieldUpdateOperationsInput | string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: NullableStringFieldUpdateOperationsInput | string | null
    retry_count?: NullableIntFieldUpdateOperationsInput | number | null
    last_retry?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    medication?: medicationsUpdateOneRequiredWithoutRemindersNestedInput
    user?: usersUpdateOneRequiredWithoutRemindersNestedInput
  }

  export type remindersUncheckedUpdateWithoutAdherenceInput = {
    id?: StringFieldUpdateOperationsInput | string
    user_id?: StringFieldUpdateOperationsInput | string
    medication_id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: NullableStringFieldUpdateOperationsInput | string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: NullableStringFieldUpdateOperationsInput | string | null
    retry_count?: NullableIntFieldUpdateOperationsInput | number | null
    last_retry?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type remindersUncheckedUpdateManyWithoutAdherenceInput = {
    id?: StringFieldUpdateOperationsInput | string
    user_id?: StringFieldUpdateOperationsInput | string
    medication_id?: StringFieldUpdateOperationsInput | string
    scheduled_time?: StringFieldUpdateOperationsInput | string
    scheduled_date?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: NullableStringFieldUpdateOperationsInput | string | null
    channels?: NullableJsonNullValueInput | InputJsonValue
    message?: NullableStringFieldUpdateOperationsInput | string | null
    retry_count?: NullableIntFieldUpdateOperationsInput | number | null
    last_retry?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    created_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    updated_at?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }



  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}