import { Modu<PERSON> } from '@nestjs/common';
import { GetAdherenceHistoryUseCase } from '../../application/adherence/use-cases/get-adherence-history.use-case';
import { ConfirmDoseUseCase } from '../../application/adherence/use-cases/confirm-dose.use-case';
import { SkipDoseUseCase } from '../../application/adherence/use-cases/skip-dose.use-case';
import { PrismaService } from '../../infrastructure/prisma/prisma.service';
import { SupabaseAdherenceRepository } from '../../infrastructure/adherence/repositories/supabase-adherence.repository';
import { AdherenceController } from './http/controllers/adherence.controller';

@Module({
  controllers: [AdherenceController],
  providers: [
    PrismaService,
    GetAdherenceHistoryUseCase,
    ConfirmDoseUseCase,
    SkipDoseUseCase,
    {
      provide: 'AdherenceRepository',
      useClass: SupabaseAdherenceRepository,
    },
  ],
})
export class AdherenceModule {}
