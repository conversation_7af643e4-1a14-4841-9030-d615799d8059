{"name": "medication-adherence-frontend", "version": "1.0.0", "private": true, "type": "module", "description": "Frontend React app for medication adherence tracking system", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write \"src/**/*.{ts,tsx}\""}, "dependencies": {"@mercadopago/sdk-react": "^1.0.3", "@sentry/react": "^9.27.0", "@sentry/tracing": "^7.120.3", "@sentry/types": "^9.27.0", "@supabase/supabase-js": "^2.39.0", "@tanstack/react-query": "^5.79.2", "axios": "^1.5.0", "chart.js": "^4.3.3", "clsx": "^2.1.0", "date-fns": "^2.30.0", "i18next": "^25.2.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-hook-form": "^7.57.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.5.2", "react-router-dom": "^6.15.0", "tailwind-merge": "^2.2.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/rollup-plugin-visualizer": "^5.0.3", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "jsdom": "^26.1.0", "postcss": "^8.4.35", "rollup-plugin-visualizer": "^6.0.1", "sharp": "^0.33.2", "tailwindcss": "^3.4.1", "terser": "^5.41.0", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vite-plugin-pwa": "^1.0.0", "vitest": "^3.2.2", "webpack-bundle-analyzer": "^4.10.2"}}