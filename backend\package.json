{"name": "medication-adherence-backend", "version": "1.0.0", "type": "module", "description": "Backend API for medication adherence tracking system", "scripts": {"dev": "node src/server.js", "start": "node src/server.js", "seed": "tsx src/scripts/seedTestData.ts", "seed:user": "tsx src/scripts/seedSpecificUser.ts"}, "dependencies": {"@sendgrid/mail": "^8.1.5", "@supabase/supabase-js": "^2.39.0", "axios": "^1.5.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "concurrently": "^8.2.2", "cors": "^2.8.5", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "mercadopago": "^2.7.0", "ml-knn": "^3.0.0", "morgan": "^1.10.0", "node-cron": "^3.0.2", "nodemailer": "^7.0.3", "stripe": "^18.2.1", "winston": "^3.10.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/supertest": "^6.0.3", "jest": "^29.7.0", "supertest": "^7.1.1", "tsx": "^4.7.1", "typescript": "^5.5.3"}, "engines": {"node": ">=18.0.0"}}