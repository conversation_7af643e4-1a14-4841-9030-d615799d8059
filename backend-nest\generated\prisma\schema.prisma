generator client {
  provider        = "prisma-client-js"
  output          = "../generated/prisma"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["public"]
}

model users {
  id                      String         @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  email                   String?        @unique
  name                    String?
  password                String?
  date_of_birth           DateTime?      @db.Timestamp(6)
  gender                  String?
  allergies               String[]
  conditions              String[]
  is_admin                <PERSON>?       @default(false)
  phone_number            String?
  emergency_contact       Json?
  created_at              DateTime?      @default(now()) @db.Timestamp(6)
  updated_at              DateTime?      @default(now()) @updatedAt @db.Timestamp(6)
  subscription_status     String?
  subscription_plan       String?
  subscription_expires_at DateTime?      @db.Timestamp(6)
  subscription_features   Json?
  adherence               adherence[]
  medication              medications[]
  reminders               reminders[]
  settings                user_settings?

  @@map("users")
  @@schema("public")
}

model user_settings {
  id                       String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  user_id                  String    @unique @db.Uuid
  email_enabled            Boolean
  preferred_times          String[]
  timezone                 String
  notification_preferences Json?
  created_at               DateTime? @default(now()) @db.Timestamp(6)
  updated_at               DateTime? @default(now()) @updatedAt @db.Timestamp(6)
  user                     users     @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "fk_user_settings_user")

  @@map("user_settings")
  @@schema("public")
}

model medications {
  id                    String      @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  user_id               String      @db.Uuid
  name                  String
  dosage                Json        @db.Json
  frequency             Json        @db.Json
  scheduled_times       String[]
  instructions          String?
  start_date            DateTime?   @default(now()) @db.Timestamptz(6)
  end_date              DateTime?   @db.Timestamptz(6)
  refill_reminder       Json?       @db.Json
  side_effects_to_watch String[]
  active                Boolean?    @default(true)
  medication_type       String?
  image_url             String?
  created_at            DateTime?   @default(now()) @db.Timestamptz(6)
  updated_at            DateTime?   @default(now()) @db.Timestamptz(6)
  adherence             adherence[]
  user                  users       @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "fk_medications_user")
  reminders             reminders[]

  @@index([user_id], map: "idx_medications_user_id")
  @@map("medications")
  @@schema("public")
}

model adherence {
  id                    String      @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  user_id               String      @db.Uuid
  medication_id         String      @db.Uuid
  scheduled_time        String
  scheduled_date        DateTime    @db.Date
  taken_time            DateTime?   @db.Timestamptz(6)
  status                String?     @default("pending")
  notes                 String?
  reminder_sent         Boolean?    @default(false)
  side_effects_reported String[]
  dosage_taken          Json?       @db.Json
  created_at            DateTime?   @default(now()) @db.Timestamptz(6)
  updated_at            DateTime?   @default(now()) @db.Timestamptz(6)
  medication            medications @relation(fields: [medication_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "fk_adherence_medication")
  user                  users       @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "fk_adherence_user")
  reminders             reminders[]

  @@index([medication_id], map: "idx_adherence_medication_id")
  @@index([user_id], map: "idx_adherence_user_id")
  @@map("adherence")
  @@schema("public")
}

model reminders {
  id             String      @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  user_id        String      @db.Uuid
  medication_id  String      @db.Uuid
  scheduled_time String
  scheduled_date DateTime    @db.Date
  status         String?     @default("pending")
  channels       Json?       @default("{\"email\": {\"enabled\": true, \"sent\": false}, \"sms\": {\"enabled\": false, \"sent\": false}}") @db.Json
  message        String?
  retry_count    Int?        @default(0)
  last_retry     DateTime?   @db.Timestamptz(6)
  adherence_id   String?     @db.Uuid
  created_at     DateTime?   @default(now()) @db.Timestamptz(6)
  updated_at     DateTime?   @default(now()) @db.Timestamptz(6)
  adherence      adherence?  @relation(fields: [adherence_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "fk_reminders_adherence")
  medication     medications @relation(fields: [medication_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "fk_reminders_medication")
  user           users       @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "fk_reminders_user")

  @@index([adherence_id], map: "idx_reminders_adherence_id")
  @@index([medication_id], map: "idx_reminders_medication_id")
  @@index([user_id], map: "idx_reminders_user_id")
  @@map("reminders")
  @@schema("public")
}
