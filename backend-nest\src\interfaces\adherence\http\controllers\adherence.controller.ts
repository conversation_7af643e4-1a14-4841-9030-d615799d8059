import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ConfirmDoseUseCase } from 'src/application/adherence/use-cases/confirm-dose.use-case';
import { GetAdherenceHistoryUseCase } from 'src/application/adherence/use-cases/get-adherence-history.use-case';
import { SkipDoseUseCase } from 'src/application/adherence/use-cases/skip-dose.use-case';
import { GetUserId } from 'src/auth/get-user-id.decorator';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { AdherencePresenter } from 'src/domain/adherence/presenters/adherence.presenter';
import { ConfirmDoseDto } from 'src/infrastructure/adherence/dtos/confirm-dose.dto';
import { GetAdherenceHistoryDto } from 'src/infrastructure/adherence/dtos/get-adherence-history.dto';
import { SkipDoseDto } from 'src/infrastructure/adherence/dtos/skip-dose.dto';

@Controller('adherence')
export class AdherenceController {
  constructor(
    private readonly getAdherenceHistoryUseCase: GetAdherenceHistoryUseCase,
    private readonly confirmDoseUseCase: ConfirmDoseUseCase,
    private readonly skipDoseUseCase: SkipDoseUseCase,
  ) {}

  @Get('history')
  @UseGuards(JwtAuthGuard)
  async getHistory(
    @Query() query: GetAdherenceHistoryDto,
    @Request() req: any,
    @GetUserId() userId: string,
  ) {
    const adherence = await this.getAdherenceHistoryUseCase.execute(
      userId,
      query.date,
    );
    return AdherencePresenter.toHttp(adherence);
  }

  @Post('confirm')
  @UseGuards(JwtAuthGuard)
  async confirmDose(
    @Body() body: ConfirmDoseDto,
    @Request() req: any,
    @GetUserId() userId: string,
  ) {
    return await this.confirmDoseUseCase.execute(body.adherenceId, userId);
  }

  @Post('skip')
  @UseGuards(JwtAuthGuard)
  async skipDose(
    @Body() body: SkipDoseDto,
    @Request() req: any,
    @GetUserId() userId: string,
  ) {
    return await this.skipDoseUseCase.execute(body.adherenceId, userId);
  }
}
